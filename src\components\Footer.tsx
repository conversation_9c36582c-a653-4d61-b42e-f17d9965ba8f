
import { Github, <PERSON>edin, Mail, Calendar, Award } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-muted/50 border-t">
      <div className="container px-4 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Brand */}
            <div>
              <h3 className="font-bold text-2xl mb-4 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                <PERSON><PERSON><PERSON>
              </h3>
              <p className="text-muted-foreground mb-4 leading-relaxed">
                Frontend Specialist & Cloud Practitioner passionate about crafting exceptional 
                web experiences and building scalable solutions for the African tech ecosystem.
              </p>
              <div className="flex items-center gap-2 mb-4 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 px-3 py-2 rounded-full w-fit">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Open to New Opportunities
              </div>
              <div className="flex gap-4">
                <a href="https://github.com/yorliabdulai" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors hover:scale-110 transform">
                  <Github size={20} />
                </a>
                <a href="https://linkedin.com/in/abdulai-yorli-iddrisu" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors hover:scale-110 transform">
                  <Linkedin size={20} />
                </a>
                <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-primary transition-colors hover:scale-110 transform">
                  <Mail size={20} />
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-semibold mb-4 text-primary">Quick Links</h4>
              <div className="space-y-3">
                <div><a href="#about" className="text-muted-foreground hover:text-primary transition-colors hover:underline">About</a></div>
                <div><a href="#projects" className="text-muted-foreground hover:text-primary transition-colors hover:underline">Projects</a></div>
                <div><a href="#contact" className="text-muted-foreground hover:text-primary transition-colors hover:underline">Contact</a></div>
                <div><a href="https://github.com/yorliabdulai" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors hover:underline">GitHub Portfolio</a></div>
                <div><a href="#" className="text-muted-foreground hover:text-primary transition-colors hover:underline">Download Resume</a></div>
              </div>
            </div>

            {/* Enhanced Contact Info */}
            <div>
              <h4 className="font-semibold mb-4 text-primary">Professional Info</h4>
              <div className="space-y-3 text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-lg">🇬🇭</span>
                  <span>Ghana, West Africa</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span>BSc Computer Science (Aug 2025)</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="w-4 h-4" />
                  <span>AWS & KCNA Certified</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-lg">💼</span>
                  <span>Available for Frontend/Full-Stack Roles</span>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; {currentYear} Abdulai Yorli Iddrisu. Crafted with React, TypeScript & Tailwind CSS.</p>
            <p className="text-sm mt-2">Building the future of African technology, one line of code at a time.</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
