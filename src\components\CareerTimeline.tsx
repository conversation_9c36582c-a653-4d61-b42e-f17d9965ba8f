import { GraduationCap, Award, Briefcase, Calendar, MapPin, Building } from "lucide-react";
import { useTranslation } from "react-i18next";

const CareerTimeline = () => {
  const { t } = useTranslation();

  const timelineEvents = [
    {
      year: "2021-2022",
      title: "Diploma in Computer Science",
      organization: "CK Tedam University of Technology and Applied Sciences",
      type: "education",
      icon: <GraduationCap className="w-5 h-5" />,
      description: "Foundation in computer science principles and programming",
      location: "Ghana",
      status: "completed"
    },
    {
      year: "2023-2025",
      title: "BSc Computer Science",
      organization: "University for Development Studies",
      type: "education",
      icon: <GraduationCap className="w-5 h-5" />,
      description: "Advanced computer science degree with focus on software development",
      location: "Ghana",
      status: "ongoing"
    },
    {
      year: "2024",
      title: "AWS re/Start Program",
      organization: "Amazon Web Services",
      type: "training",
      icon: <Award className="w-5 h-5" />,
      description: "Intensive cloud computing training program",
      location: "Remote",
      status: "completed"
    },
    {
      year: "2024",
      title: "AWS Cloud Practitioner",
      organization: "Amazon Web Services",
      type: "certification",
      icon: <Award className="w-5 h-5" />,
      description: "Foundational cloud computing certification",
      location: "Remote",
      status: "completed"
    },
    {
      year: "2024",
      title: "Software Development Intern",
      organization: "Amalitech",
      type: "internship",
      icon: <Briefcase className="w-5 h-5" />,
      description: "Full-stack development with modern web technologies",
      location: "Ghana",
      status: "completed"
    },
    {
      year: "2024",
      title: "Technical Intern",
      organization: "Wikimedia Deutschland",
      type: "internship",
      icon: <Briefcase className="w-5 h-5" />,
      description: "Open source development and community contribution",
      location: "Germany (Remote)",
      status: "completed"
    },
    {
      year: "2024",
      title: "IT Intern",
      organization: "Agricultural Development Bank",
      type: "internship",
      icon: <Briefcase className="w-5 h-5" />,
      description: "Banking technology systems and digital transformation",
      location: "Ghana",
      status: "completed"
    },
    {
      year: "2025",
      title: "Final Year Project",
      organization: "University for Development Studies",
      type: "education",
      icon: <GraduationCap className="w-5 h-5" />,
      description: "Capstone project demonstrating advanced software development skills",
      location: "Ghana",
      status: "ongoing"
    },
    {
      year: "2025",
      title: "KCNA Certification",
      organization: "Cloud Native Computing Foundation",
      type: "certification",
      icon: <Award className="w-5 h-5" />,
      description: "Kubernetes and Cloud Native Associate certification",
      location: "Remote",
      status: "planned"
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case "education": return "from-blue-500 to-blue-600";
      case "certification": return "from-green-500 to-green-600";
      case "internship": return "from-purple-500 to-purple-600";
      case "training": return "from-orange-500 to-orange-600";
      default: return "from-gray-500 to-gray-600";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "ongoing": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "planned": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <section className="py-32 bg-gradient-to-br from-background via-primary/5 to-accent/5 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '3s' }}></div>
      </div>

      <div className="container px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-6 py-3 rounded-full text-sm font-medium mb-8 animate-fade-in-up">
              <Calendar className="w-4 h-4" />
              Career Journey
            </div>

            <h2 className="text-5xl md:text-7xl font-display font-bold mb-8 bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent animate-gradient-shift bg-[length:400%_400%]">
              {t("careerTimeline")}
            </h2>

            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-light">
              {t("careerTimelineDesc")}
            </p>
          </div>

          {/* Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary to-accent"></div>

            {/* Timeline Events */}
            <div className="space-y-12">
              {timelineEvents.map((event, index) => (
                <div
                  key={index}
                  className="relative flex items-start gap-8 animate-fade-in-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Timeline Dot */}
                  <div className={`relative z-10 w-16 h-16 rounded-full bg-gradient-to-br ${getTypeColor(event.type)} flex items-center justify-center text-white shadow-lg hover:scale-110 transition-transform duration-300`}>
                    {event.icon}
                  </div>

                  {/* Content Card */}
                  <div className="flex-1 bg-card/50 backdrop-blur-sm rounded-2xl p-8 border shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-1 group">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
                      <div>
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                            {event.title}
                          </h3>
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadge(event.status)}`}>
                            {event.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-primary font-semibold mb-2">
                          <Building className="w-4 h-4" />
                          {event.organization}
                        </div>
                        <div className="flex items-center gap-2 text-muted-foreground text-sm">
                          <MapPin className="w-4 h-4" />
                          {event.location}
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-accent">
                        {event.year}
                      </div>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      {event.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-primary to-accent text-white px-8 py-4 rounded-full font-semibold hover:scale-105 transition-transform duration-300 cursor-pointer shadow-xl">
              <Award className="w-5 h-5" />
              Continuous Learning Journey
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CareerTimeline;
