<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="400" fill="url(#gradient)"/>
  
  <!-- Professional silhouette -->
  <circle cx="200" cy="160" r="60" fill="#1e293b" opacity="0.8"/>
  <path d="M120 320 C120 280, 150 250, 200 250 C250 250, 280 280, 280 320 L280 400 L120 400 Z" fill="#1e293b" opacity="0.8"/>
  
  <!-- Overlay text -->
  <text x="200" y="350" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="16" font-weight="600">
    Professional Headshot
  </text>
  <text x="200" y="370" text-anchor="middle" fill="#94a3b8" font-family="Arial, sans-serif" font-size="12">
    Replace with your photo
  </text>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
