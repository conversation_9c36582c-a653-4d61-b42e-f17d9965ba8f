// Advanced Magnetic Hover Effect
export const initMagneticHover = () => {
  const magneticElements = document.querySelectorAll('.magnetic-hover');
  
  magneticElements.forEach((element) => {
    const htmlElement = element as HTMLElement;
    
    const handleMouseMove = (e: MouseEvent) => {
      const rect = htmlElement.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;
      
      const distance = Math.sqrt(x * x + y * y);
      const maxDistance = Math.max(rect.width, rect.height) / 2;
      
      if (distance < maxDistance) {
        const strength = (maxDistance - distance) / maxDistance;
        const moveX = (x / maxDistance) * 20 * strength;
        const moveY = (y / maxDistance) * 20 * strength;
        
        htmlElement.style.setProperty('--x', `${moveX}px`);
        htmlElement.style.setProperty('--y', `${moveY}px`);
        htmlElement.style.transform = `translate(${moveX}px, ${moveY}px) scale(1.05)`;
      }
    };
    
    const handleMouseLeave = () => {
      htmlElement.style.setProperty('--x', '0px');
      htmlElement.style.setProperty('--y', '0px');
      htmlElement.style.transform = 'translate(0px, 0px) scale(1)';
    };
    
    htmlElement.addEventListener('mousemove', handleMouseMove);
    htmlElement.addEventListener('mouseleave', handleMouseLeave);
  });
};

// Parallax scroll effect
export const initParallaxScroll = () => {
  const parallaxElements = document.querySelectorAll('.parallax-element');
  
  const handleScroll = () => {
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;
    
    parallaxElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      htmlElement.style.transform = `translateY(${rate}px)`;
    });
  };
  
  window.addEventListener('scroll', handleScroll);
};

// Intersection Observer for animations
export const initScrollAnimations = () => {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
      }
    });
  }, observerOptions);
  
  const animatedElements = document.querySelectorAll('[data-animate]');
  animatedElements.forEach((element) => observer.observe(element));
};

// Smooth cursor following effect
export const initCursorFollower = () => {
  const cursor = document.createElement('div');
  cursor.className = 'cursor-follower';
  cursor.style.cssText = `
    position: fixed;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    mix-blend-mode: difference;
  `;
  document.body.appendChild(cursor);
  
  let mouseX = 0;
  let mouseY = 0;
  let cursorX = 0;
  let cursorY = 0;
  
  const updateCursor = () => {
    cursorX += (mouseX - cursorX) * 0.1;
    cursorY += (mouseY - cursorY) * 0.1;
    
    cursor.style.left = `${cursorX - 10}px`;
    cursor.style.top = `${cursorY - 10}px`;
    
    requestAnimationFrame(updateCursor);
  };
  
  document.addEventListener('mousemove', (e) => {
    mouseX = e.clientX;
    mouseY = e.clientY;
  });
  
  updateCursor();
};

// Text reveal animation
export const initTextReveal = () => {
  const textElements = document.querySelectorAll('.text-reveal');
  
  textElements.forEach((element) => {
    const text = element.textContent || '';
    element.innerHTML = '';
    
    text.split('').forEach((char, index) => {
      const span = document.createElement('span');
      span.textContent = char === ' ' ? '\u00A0' : char;
      span.style.animationDelay = `${index * 0.05}s`;
      span.className = 'inline-block animate-fade-in-up';
      element.appendChild(span);
    });
  });
};

// Initialize all effects
export const initAdvancedAnimations = () => {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      initMagneticHover();
      initParallaxScroll();
      initScrollAnimations();
      initTextReveal();
    });
  } else {
    initMagneticHover();
    initParallaxScroll();
    initScrollAnimations();
    initTextReveal();
  }
};
