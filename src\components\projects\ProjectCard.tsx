
import { Gith<PERSON>, ExternalLink, ArrowUpR<PERSON>, Star, TrendingUp, Lock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "react-i18next";
import { useState } from "react";

interface ProjectCardProps {
  title: string;
  description: string;
  image: string;
  tags: string[];
  liveUrl: string;
  githubUrl: string;
  contractUrl?: string; // Optional smart contract repository
  impact: string;
  icon: React.ReactNode;
}

const ProjectCard = ({ title, description, image, tags, liveUrl, githubUrl, contractUrl, impact, icon }: ProjectCardProps) => {
  const { t } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);

  const isPrivateRepo = githubUrl === "private" || githubUrl === "#";

  return (
    <Card
      className="group relative overflow-hidden border-0 bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl shadow-2xl hover:shadow-[0_25px_50px_-12px_rgba(0,0,0,0.25)] transition-all duration-700 hover:-translate-y-2 hover:scale-[1.02]"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Animated Border */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg blur-sm"></div>

      <CardHeader className="p-0 relative">
        <div className="relative overflow-hidden rounded-t-lg">
          {/* Image with Advanced Effects */}
          <div className="relative h-56 overflow-hidden">
            <img
              src={image}
              alt={title}
              className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:rotate-1"
            />

            {/* Gradient Overlays */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-500"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-primary/30 to-accent/30 opacity-0 group-hover:opacity-40 transition-opacity duration-500"></div>
          </div>

          {/* Floating Icon */}
          <div className="absolute top-6 left-6 group-hover:scale-110 transition-transform duration-300">
            <div className="relative">
              <div className="absolute inset-0 bg-white/20 backdrop-blur-md rounded-2xl blur-sm"></div>
              <div className="relative bg-white/90 backdrop-blur-md rounded-2xl p-3 shadow-xl border border-white/20">
                {icon}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className={`absolute top-6 right-6 flex gap-2 transition-all duration-500 ${isHovered ? 'translate-x-0 opacity-100' : 'translate-x-8 opacity-0'}`}>
            <Button asChild size="sm" className="rounded-full bg-white/20 backdrop-blur-md border border-white/20 text-white hover:bg-white hover:text-black transition-all duration-300">
              <a href={liveUrl} target="_blank" rel="noopener noreferrer">
                <ArrowUpRight className="w-4 h-4" />
              </a>
            </Button>
            <Button asChild size="sm" className="rounded-full bg-white/20 backdrop-blur-md border border-white/20 text-white hover:bg-white hover:text-black transition-all duration-300">
              <a href={githubUrl} target="_blank" rel="noopener noreferrer">
                <Github className="w-4 h-4" />
              </a>
            </Button>
          </div>

          {/* Impact Badge */}
          <div className="absolute bottom-6 left-6 right-6">
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-3 border border-white/20">
              <div className="flex items-center gap-2 text-white">
                <TrendingUp className="w-4 h-4 text-green-400" />
                <span className="text-sm font-medium">{impact}</span>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-8 relative">
        {/* Title with Gradient */}
        <CardTitle className="text-2xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent group-hover:from-primary group-hover:to-accent transition-all duration-500">
          {title}
        </CardTitle>

        {/* Description */}
        <CardDescription className="text-muted-foreground mb-6 leading-relaxed text-base">
          {description}
        </CardDescription>

        {/* Premium Tech Stack */}
        <div className="flex flex-wrap gap-2 mb-6">
          {tags.map((tag, tagIndex) => (
            <Badge
              key={tagIndex}
              className="bg-gradient-to-r from-primary/10 to-accent/10 text-foreground border border-primary/20 hover:from-primary hover:to-accent hover:text-white transition-all duration-300 hover:scale-105 px-3 py-1 text-sm font-medium"
            >
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>

      <CardFooter className="p-8 pt-0 flex gap-3">
        {liveUrl && liveUrl !== "#" ? (
          <Button
            asChild
            className="flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white font-semibold py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg"
          >
            <a href={liveUrl} target="_blank" rel="noopener noreferrer" className="flex items-center justify-center gap-2">
              <ExternalLink className="w-5 h-5" />
              {t("liveDemo")}
            </a>
          </Button>
        ) : (
          <Button
            disabled
            className="flex-1 bg-muted text-muted-foreground py-3 rounded-xl cursor-not-allowed"
          >
            <span className="flex items-center justify-center gap-2">
              <ExternalLink className="w-5 h-5" />
              {t("liveDemo")} - Coming Soon
            </span>
          </Button>
        )}

        {isPrivateRepo ? (
          <Button
            variant="outline"
            disabled
            className="px-6 py-3 rounded-xl border-2 border-muted-foreground/20 bg-muted/50 text-muted-foreground cursor-not-allowed"
            title={t("privateRepository")}
          >
            <div className="flex items-center justify-center gap-2">
              <Lock className="w-4 h-4" />
              <Github className="w-4 h-4" />
            </div>
          </Button>
        ) : (
          <Button
            asChild
            variant="outline"
            className="px-6 py-3 rounded-xl border-2 border-primary/20 hover:border-primary hover:bg-primary hover:text-white transition-all duration-300 hover:scale-105"
          >
            <a href={githubUrl} target="_blank" rel="noopener noreferrer" className="flex items-center justify-center">
              <Github className="w-5 h-5" />
            </a>
          </Button>
        )}

        {contractUrl && (
          <Button
            asChild
            variant="outline"
            size="sm"
            className="px-4 py-3 rounded-xl border-2 border-accent/20 hover:border-accent hover:bg-accent hover:text-white transition-all duration-300 hover:scale-105"
            title={t("viewContract")}
          >
            <a href={contractUrl} target="_blank" rel="noopener noreferrer" className="flex items-center justify-center gap-2">
              <span className="text-sm">⚡</span>
              <span className="text-xs font-medium hidden sm:inline">{t("smartContract")}</span>
            </a>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default ProjectCard;
