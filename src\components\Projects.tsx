
import { Github, ZapIcon, Calendar, FileTextIcon, FolderIcon, Building2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import ProjectCard from "./projects/ProjectCard";
import { useTranslation } from "react-i18next";

const Projects = () => {
  const { t } = useTranslation();

  const projects = [
    {
      title: "PayVoicer",
      description: t("payvoicerDesc"),
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop",
      tags: ["React", "Node.js", "Speech API", "MongoDB", "Stripe"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("payvoicerImpact"),
      icon: <ZapIcon className="w-5 h-5" />
    },
    {
      title: "CEM Newsroom",
      description: t("cemNewsroomDesc"),
      image: "https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=500&h=300&fit=crop",
      tags: ["Next.js", "Sanity CMS", "Tailwind", "TypeScript"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("cemNewsroomImpact"),
      icon: <FileTextIcon className="w-5 h-5" />
    },
    {
      title: "Last Stop Enterprise",
      description: t("lastStopEnterpriseDesc"),
      image: "https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=500&h=300&fit=crop",
      tags: ["React", "Express", "PostgreSQL", "Socket.io", "Maps API"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("lastStopEnterpriseImpact"),
      icon: <FolderIcon className="w-5 h-5" />
    },
    {
      title: "GeomancyCoaching.org",
      description: t("geomancyCoachingDesc"),
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=300&fit=crop",
      tags: ["WordPress", "PHP", "MySQL", "Payment Gateway"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("geomancyCoachingImpact"),
      icon: <Calendar className="w-5 h-5" />
    },
    {
      title: "Hostel Booking System",
      description: t("hostelBookingDesc"),
      image: "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=500&h=300&fit=crop",
      tags: ["React", "Node.js", "Stripe", "MongoDB", "Socket.io"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("hostelBookingImpact"),
      icon: <FolderIcon className="w-5 h-5" />
    },
    {
      title: "Landform DApp",
      description: t("landformDappDesc"),
      image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=300&fit=crop",
      tags: ["Blockchain", "React", "Web3", "Smart Contracts", "Real Estate"],
      liveUrl: "https://landformdapp.netlify.app/",
      githubUrl: "#",
      impact: t("landformDappImpact"),
      icon: <Building2 className="w-5 h-5" />
    }
  ];

  return (
    <section id="projects" className="py-32 bg-gradient-to-br from-background via-primary/5 to-accent/5 relative overflow-hidden">
      {/* Advanced Background System */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Revolutionary Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-6 py-3 rounded-full text-sm font-medium mb-8 animate-fade-in-up">
              <span className="w-2 h-2 bg-primary rounded-full animate-pulse"></span>
              Portfolio Showcase
            </div>

            <h2 className="text-5xl md:text-7xl font-display font-bold mb-8 bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent animate-gradient-shift bg-[length:400%_400%]">
              {t("featuredProjects")}
            </h2>

            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-light">
              {t("featuredProjectsDesc")}
            </p>
          </div>

          {/* Premium Project Grid */}
          <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-16">
            {projects.map((project, index) => (
              <div
                key={index}
                className="animate-fade-in-up"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <ProjectCard {...project} />
              </div>
            ))}
          </div>

          {/* Premium CTA Section */}
          <div className="text-center">
            <div className="relative inline-block group">
              <div className="absolute -inset-1 bg-gradient-to-r from-primary to-accent rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000"></div>
              <Button
                asChild
                size="lg"
                className="relative bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white px-12 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105 shadow-xl text-lg"
              >
                <a href="https://github.com/yorliabdulai" target="_blank" rel="noopener noreferrer" className="flex items-center gap-3">
                  <Github className="w-6 h-6" />
                  {t("viewMoreOnGithub")}
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
