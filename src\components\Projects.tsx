
import { Gith<PERSON> } from "lucide-react";
import { SiWeb3Dotjs } from "react-icons/si";
import { MdPayment } from "react-icons/md";
import { FaNewspaper, FaTruck, FaCalendarAlt, FaHotel } from "react-icons/fa";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ProjectCard from "./projects/ProjectCard";
import { useTranslation } from "react-i18next";

const Projects = () => {
  const { t } = useTranslation();

  const projects = [
    {
      title: "Payvoicer",
      description: t("payvoicerDesc"),
      image: "/payvoicer.PNG",
      tags: ["React", "TypeScript", "Supabase", "Paystack", "GRA API", "SaaS"],
      liveUrl: "https://payvoicer.netlify.app/",
      githubUrl: "private",
      impact: t("payvoicerImpact"),
      icon: <MdPayment className="w-5 h-5" />
    },
    {
      title: "CEM Newsroom",
      description: t("cemNewsroomDesc"),
      image: "/cemnewsroom.PNG",
      tags: ["React", "TypeScript", "Supabase", "Tailwind CSS", "React Query", "Radix UI"],
      liveUrl: "https://cemnewsroom.netlify.app/",
      githubUrl: "private",
      impact: t("cemNewsroomImpact"),
      icon: <FaNewspaper className="w-5 h-5" />
    },
    {
      title: "Last Stop Enterprise",
      description: t("lastStopEnterpriseDesc"),
      image: "/laststop.PNG",
      tags: ["React", "Express", "Firebase", "Supabase", "Redux Toolkit", "Stripe", "Paystack"],
      liveUrl: "https://laststopenterprise.vercel.app/",
      githubUrl: "https://github.com/yorliabdulai/laststopenterprise",
      impact: t("lastStopEnterpriseImpact"),
      icon: <FaTruck className="w-5 h-5" />
    },
    {
      title: "GeomancyCoaching.org",
      description: t("geomancyCoachingDesc"),
      image: "/geomancy-coaching.svg",
      tags: ["React", "TypeScript", "Vite", "Tailwind CSS", "Lucide Icons"],
      liveUrl: "#",
      githubUrl: "private",
      impact: t("geomancyCoachingImpact"),
      icon: <FaCalendarAlt className="w-5 h-5" />
    },

    {
      title: "Hostel Booking System",
      description: t("hostelBookingDesc"),
      image: "/hostelman.PNG",
      tags: ["Next.js", "TypeScript", "Auth0", "Sanity CMS", "Tailwind CSS"],
      liveUrl: "https://hostel-booking-three.vercel.app/",
      githubUrl: "https://github.com/yorliabdulai/Hotel-Management",
      impact: t("hostelBookingImpact"),
      icon: <FaHotel className="w-5 h-5" />
    },
    {
      title: "Landform DApp",
      description: t("landformDappDesc"),
      image: "/landform.PNG",
      tags: ["React", "TypeScript", "Web3", "NFTs", "IPFS", "Wagmi", "RainbowKit"],
      liveUrl: "https://landformdapp.netlify.app/",
      githubUrl: "https://github.com/yorliabdulai/LandFormDapp",
      contractUrl: "https://github.com/yorliabdulai/LandFormContract",
      impact: t("landformDappImpact"),
      icon: <SiWeb3Dotjs className="w-5 h-5" />
    }
  ];

  return (
    <section id="projects" className="py-32 bg-gradient-to-br from-background via-primary/5 to-accent/5 relative overflow-hidden">
      {/* Advanced Background System */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Revolutionary Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-6 py-3 rounded-full text-sm font-medium mb-8 animate-fade-in-up">
              <span className="w-2 h-2 bg-primary rounded-full animate-pulse"></span>
              Portfolio Showcase
            </div>

            <h2 className="text-5xl md:text-7xl font-display font-bold mb-8 bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent animate-gradient-shift bg-[length:400%_400%]">
              {t("featuredProjects")}
            </h2>

            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-light">
              {t("featuredProjectsDesc")}
            </p>
          </div>

          {/* Premium Project Grid */}
          <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-16">
            {projects.map((project, index) => (
              <div
                key={index}
                className="animate-fade-in-up"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <ProjectCard {...project} />
              </div>
            ))}
          </div>

          {/* Premium CTA Section */}
          <div className="text-center">
            <div className="relative inline-block group">
              <div className="absolute -inset-1 bg-gradient-to-r from-primary to-accent rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000"></div>
              <Button
                asChild
                size="lg"
                className="relative bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white px-12 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105 shadow-xl text-lg"
              >
                <a href="https://github.com/yorliabdulai" target="_blank" rel="noopener noreferrer" className="flex items-center gap-3">
                  <Github className="w-6 h-6" />
                  {t("viewMoreOnGithub")}
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
