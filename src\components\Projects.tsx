
import { Github, ZapIcon, Calendar, FileTextIcon, FolderIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import ProjectCard from "./projects/ProjectCard";
import { useTranslation } from "react-i18next";

const Projects = () => {
  const { t } = useTranslation();
  
  const projects = [
    {
      title: "PayVoicer",
      description: t("payvoicerDesc"),
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop",
      tags: ["React", "Node.js", "Speech API", "MongoDB", "Stripe"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("payvoicerImpact"),
      icon: <ZapIcon className="w-5 h-5" />
    },
    {
      title: "CEM Newsroom",
      description: t("cemNewsroomDesc"),
      image: "https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=500&h=300&fit=crop",
      tags: ["Next.js", "Sanity CMS", "Tailwind", "TypeScript"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("cemNewsroomImpact"),
      icon: <FileTextIcon className="w-5 h-5" />
    },
    {
      title: "Last Stop Enterprise",
      description: t("lastStopEnterpriseDesc"),
      image: "https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=500&h=300&fit=crop",
      tags: ["React", "Express", "PostgreSQL", "Socket.io", "Maps API"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("lastStopEnterpriseImpact"),
      icon: <FolderIcon className="w-5 h-5" />
    },
    {
      title: "GeomancyCoaching.org",
      description: t("geomancyCoachingDesc"),
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=300&fit=crop",
      tags: ["WordPress", "PHP", "MySQL", "Payment Gateway"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("geomancyCoachingImpact"),
      icon: <Calendar className="w-5 h-5" />
    },
    {
      title: "Hostel Booking System",
      description: t("hostelBookingDesc"),
      image: "https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=500&h=300&fit=crop",
      tags: ["React", "Node.js", "Stripe", "MongoDB", "Socket.io"],
      liveUrl: "#",
      githubUrl: "#",
      impact: t("hostelBookingImpact"),
      icon: <FolderIcon className="w-5 h-5" />
    }
  ];

  return (
    <section id="projects" className="py-20 bg-background relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="container px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
              {t("featuredProjects")}
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              {t("featuredProjectsDesc")}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {projects.map((project, index) => (
              <ProjectCard key={index} {...project} />
            ))}
          </div>

          <div className="text-center mt-12">
            <Button asChild variant="outline" size="lg" className="px-8 py-3 rounded-full hover:scale-105 transition-transform">
              <a href="https://github.com/yorliabdulai" target="_blank" rel="noopener noreferrer">
                <Github className="w-5 h-5 mr-2" />
                {t("viewMoreOnGithub")}
              </a>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
