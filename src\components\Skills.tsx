
import { Code, Cloud, Database, Globe, Wrench, Award } from "lucide-react";
import SkillCategory from "./skills/SkillCategory";
import { useTranslation } from "react-i18next";
import {
  SiReact,
  SiNextdotjs,
  SiTypescript,
  SiTailwindcss,
  SiJavascript,
  SiHtml5,
  SiCss3,
  SiNodedotjs,
  SiExpress,
  SiPython,
  SiPostgresql,
  SiMongodb,
  SiMysql,
  SiSupabase,
  SiSanity,
  SiGraphql,
  SiAmazonwebservices,
  SiGooglecloud,
  SiKubernetes,
  SiDocker,
  SiGit,
  SiGithub,
  SiVercel,
  SiNetlify,
  SiLinux,

  SiFigma,
  SiPostman,
  SiStripe,
  SiSocketdotio,
  SiFirebase,
  SiRedux,
  SiVite,
  SiWeb3Dotjs,
  SiIpfs
} from "react-icons/si";
import { FaJava } from "react-icons/fa";
import { VscCode } from "react-icons/vsc";

// Technology Icon Components with Real Logos
const TechIcon = ({ name, className = "w-5 h-5" }: { name: string; className?: string }) => {
  const icons: Record<string, JSX.Element> = {
    "React": <SiReact className={`${className} text-blue-500`} />,
    "Next.js": <SiNextdotjs className={`${className} text-black dark:text-white`} />,
    "TypeScript": <SiTypescript className={`${className} text-blue-600`} />,
    "Tailwind CSS": <SiTailwindcss className={`${className} text-cyan-500`} />,
    "JavaScript ES6+": <SiJavascript className={`${className} text-yellow-500`} />,
    "HTML5 & CSS3": <div className="flex gap-1"><SiHtml5 className={`${className} text-orange-500`} /><SiCss3 className={`${className} text-blue-500`} /></div>,
    "Node.js": <SiNodedotjs className={`${className} text-green-600`} />,
    "Express.js": <SiExpress className={`${className} text-gray-700 dark:text-gray-300`} />,
    "Python": <SiPython className={`${className} text-blue-500`} />,
    "Java": <FaJava className={`${className} text-red-600`} />,
    "PostgreSQL": <SiPostgresql className={`${className} text-blue-700`} />,
    "MongoDB": <SiMongodb className={`${className} text-green-500`} />,
    "MySQL": <SiMysql className={`${className} text-blue-800`} />,
    "Supabase": <SiSupabase className={`${className} text-green-600`} />,
    "Sanity CMS": <SiSanity className={`${className} text-red-500`} />,
    "REST APIs": <div className={`${className} bg-purple-500 rounded flex items-center justify-center text-white font-bold text-xs`}>API</div>,
    "GraphQL": <SiGraphql className={`${className} text-pink-500`} />,
    "AWS": <SiAmazonwebservices className={`${className} text-orange-400`} />,
    "GCP": <SiGooglecloud className={`${className} text-blue-500`} />,
    "Kubernetes": <SiKubernetes className={`${className} text-blue-600`} />,
    "Docker": <SiDocker className={`${className} text-blue-500`} />,
    "Git & GitHub": <div className="flex gap-1"><SiGit className={`${className} text-orange-500`} /><SiGithub className={`${className} text-gray-800 dark:text-white`} /></div>,
    "CI/CD": <div className={`${className} bg-green-600 rounded flex items-center justify-center text-white font-bold text-xs`}>CI</div>,
    "Vercel": <SiVercel className={`${className} text-black dark:text-white`} />,
    "Netlify": <SiNetlify className={`${className} text-teal-500`} />,
    "Linux": <SiLinux className={`${className} text-yellow-500`} />,
    "VS Code": <VscCode className={`${className} text-blue-600`} />,
    "Figma": <SiFigma className={`${className} text-purple-500`} />,
    "Postman": <SiPostman className={`${className} text-orange-500`} />,
    "Stripe": <SiStripe className={`${className} text-purple-600`} />,
    "Paystack": <div className={`${className} bg-blue-600 rounded flex items-center justify-center text-white font-bold text-xs`}>PS</div>,
    "Socket.io": <SiSocketdotio className={`${className} text-black dark:text-white`} />,
    "Firebase": <SiFirebase className={`${className} text-yellow-600`} />,
    "Redux Toolkit": <SiRedux className={`${className} text-purple-700`} />,
    "Vite": <SiVite className={`${className} text-purple-500`} />,
    "Wagmi": <SiWeb3Dotjs className={`${className} text-orange-500`} />,
    "Web3": <SiWeb3Dotjs className={`${className} text-orange-600`} />,
    "IPFS": <SiIpfs className={`${className} text-blue-400`} />,
    "Speech APIs": <div className={`${className} bg-red-500 rounded flex items-center justify-center text-white font-bold text-xs`}>🎤</div>,
    "KCNA (2025)": <SiKubernetes className={`${className} text-blue-600`} />,
    "AWS Cloud Practitioner": <SiAmazonwebservices className={`${className} text-orange-400`} />,
    "Google IT Support": <SiGooglecloud className={`${className} text-blue-500`} />,
    "JavaScript Algorithms": <SiJavascript className={`${className} text-yellow-500`} />
  };

  return icons[name] || <div className={`${className} bg-gray-500 rounded flex items-center justify-center text-white font-bold text-xs`}>?</div>;
};

const Skills = () => {
  const { t } = useTranslation();

  const skillCategories = [
    {
      title: t("frontendMastery"),
      icon: <Code className="w-8 h-8" />,
      gradient: "from-blue-500 to-cyan-500",
      skills: [
        { name: "React", icon: <TechIcon name="React" /> },
        { name: "Next.js", icon: <TechIcon name="Next.js" /> },
        { name: "TypeScript", icon: <TechIcon name="TypeScript" /> },
        { name: "Tailwind CSS", icon: <TechIcon name="Tailwind CSS" /> },
        { name: "JavaScript ES6+", icon: <TechIcon name="JavaScript ES6+" /> },
        { name: "HTML5 & CSS3", icon: <TechIcon name="HTML5 & CSS3" /> }
      ]
    },
    {
      title: t("backendApis"),
      icon: <Database className="w-8 h-8" />,
      gradient: "from-green-500 to-emerald-500",
      skills: [
        { name: "Node.js", icon: <TechIcon name="Node.js" /> },
        { name: "Express.js", icon: <TechIcon name="Express.js" /> },
        { name: "Python", icon: <TechIcon name="Python" /> },
        { name: "Java", icon: <TechIcon name="Java" /> },
        { name: "PostgreSQL", icon: <TechIcon name="PostgreSQL" /> },
        { name: "MongoDB", icon: <TechIcon name="MongoDB" /> },
        { name: "MySQL", icon: <TechIcon name="MySQL" /> },
        { name: "Supabase", icon: <TechIcon name="Supabase" /> },
        { name: "Firebase", icon: <TechIcon name="Firebase" /> },
        { name: "Sanity CMS", icon: <TechIcon name="Sanity CMS" /> },
        { name: "REST APIs", icon: <TechIcon name="REST APIs" /> },
        { name: "GraphQL", icon: <TechIcon name="GraphQL" /> }
      ]
    },
    {
      title: t("cloudDevops"),
      icon: <Cloud className="w-8 h-8" />,
      gradient: "from-purple-500 to-violet-500",
      skills: [
        { name: "AWS", icon: <TechIcon name="AWS" /> },
        { name: "GCP", icon: <TechIcon name="GCP" /> },
        { name: "Kubernetes", icon: <TechIcon name="Kubernetes" /> },
        { name: "Docker", icon: <TechIcon name="Docker" /> },
        { name: "Git & GitHub", icon: <TechIcon name="Git & GitHub" /> },
        { name: "CI/CD", icon: <TechIcon name="CI/CD" /> },
        { name: "Vercel", icon: <TechIcon name="Vercel" /> },
        { name: "Netlify", icon: <TechIcon name="Netlify" /> },
        { name: "Linux", icon: <TechIcon name="Linux" /> }
      ]
    },
    {
      title: t("developmentTools"),
      icon: <Wrench className="w-8 h-8" />,
      gradient: "from-orange-500 to-red-500",
      skills: [
        { name: "VS Code", icon: <TechIcon name="VS Code" /> },
        { name: "Figma", icon: <TechIcon name="Figma" /> },
        { name: "Postman", icon: <TechIcon name="Postman" /> },
        { name: "Stripe", icon: <TechIcon name="Stripe" /> },
        { name: "Paystack", icon: <TechIcon name="Paystack" /> },
        { name: "Socket.io", icon: <TechIcon name="Socket.io" /> },
        { name: "Redux Toolkit", icon: <TechIcon name="Redux Toolkit" /> },
        { name: "Vite", icon: <TechIcon name="Vite" /> },
        { name: "Web3", icon: <TechIcon name="Web3" /> },
        { name: "IPFS", icon: <TechIcon name="IPFS" /> }
      ]
    },
    {
      title: t("certificationsLearning"),
      icon: <Award className="w-8 h-8" />,
      gradient: "from-indigo-500 to-blue-500",
      skills: [
        { name: "KCNA (2025)", icon: <TechIcon name="KCNA (2025)" /> },
        { name: "AWS Cloud Practitioner", icon: <TechIcon name="AWS Cloud Practitioner" /> },
        { name: "Google IT Support", icon: <TechIcon name="Google IT Support" /> },
        { name: "JavaScript Algorithms", icon: <TechIcon name="JavaScript Algorithms" /> }
      ]
    }
  ];

  const achievements = [
    { number: "5+", label: t("yearsCoding"), icon: "💻", color: "text-blue-600" },
    { number: "15+", label: t("projectsBuilt"), icon: "🚀", color: "text-green-600" },
    { number: "5", label: t("certifications"), icon: "🏆", color: "text-purple-600" },
    { number: "100+", label: t("studentsMentored"), icon: "👥", color: "text-orange-600" }
  ];

  return (
    <section className="py-32 bg-gradient-to-br from-background via-accent/5 to-primary/5 relative overflow-hidden">
      {/* Premium Background System */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>
        <div className="absolute top-1/3 right-1/3 w-96 h-96 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/3 left-1/3 w-80 h-80 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '3s' }}></div>
      </div>

      <div className="container px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Revolutionary Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-accent/10 text-accent px-6 py-3 rounded-full text-sm font-medium mb-8 animate-fade-in-up">
              <span className="w-2 h-2 bg-accent rounded-full animate-pulse"></span>
              {t("technicalExpertise")}
            </div>

            <h2 className="text-5xl md:text-7xl font-display font-bold mb-8 bg-gradient-to-r from-accent via-primary to-accent bg-clip-text text-transparent animate-gradient-shift bg-[length:400%_400%]">
              {t("technicalArsenal")}
            </h2>

            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-light">
              {t("technicalArsenalDesc")}
            </p>
          </div>

          {/* Skills Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {skillCategories.map((category, index) => (
              <SkillCategory key={index} {...category} />
            ))}
          </div>

          {/* Achievement Highlights */}
          <div className="bg-card/30 backdrop-blur-sm rounded-2xl p-8 border shadow-sm">
            <h3 className="text-2xl font-bold text-center mb-8 text-primary">{t("professionalHighlights")}</h3>
            <div className="grid md:grid-cols-4 gap-6">
              {achievements.map((achievement, index) => (
                <div key={index} className="text-center group hover:scale-105 transition-transform duration-300">
                  <div className="mb-4 text-4xl group-hover:scale-125 transition-transform duration-300">
                    {achievement.icon}
                  </div>
                  <div className={`text-4xl font-bold ${achievement.color} mb-2 group-hover:text-primary transition-colors`}>
                    {achievement.number}
                  </div>
                  <div className="text-sm text-muted-foreground font-medium">{achievement.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-12">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full font-semibold hover:scale-105 transition-transform duration-300 cursor-pointer shadow-xl">
              <Globe className="w-5 h-5" />
              {t("readyToBuild")}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
