
import { Code, Cloud, Database, Globe, Wrench, Award } from "lucide-react";
import SkillCategory from "./skills/SkillCategory";

const Skills = () => {
  const skillCategories = [
    {
      title: "Frontend Mastery",
      icon: <Code className="w-8 h-8" />,
      gradient: "from-blue-500 to-cyan-500",
      skills: [
        { name: "React", icon: "⚛️" },
        { name: "Next.js", icon: "▲" },
        { name: "TypeScript", icon: "🔷" },
        { name: "Tailwind CSS", icon: "🎨" },
        { name: "JavaScript ES6+", icon: "🟨" },
        { name: "HTML5 & CSS3", icon: "🌐" }
      ]
    },
    {
      title: "Backend & APIs",
      icon: <Database className="w-8 h-8" />,
      gradient: "from-green-500 to-emerald-500",
      skills: [
        { name: "Node.js", icon: "🟢" },
        { name: "Express.js", icon: "🚂" },
        { name: "Python", icon: "🐍" },
        { name: "PostgreSQL", icon: "🐘" },
        { name: "MongoDB", icon: "🍃" },
        { name: "REST APIs", icon: "🔌" },
        { name: "GraphQL", icon: "⚡" }
      ]
    },
    {
      title: "Cloud & DevOps",
      icon: <Cloud className="w-8 h-8" />,
      gradient: "from-purple-500 to-violet-500",
      skills: [
        { name: "AWS", icon: "☁️" },
        { name: "Kubernetes", icon: "⎈" },
        { name: "Docker", icon: "🐳" },
        { name: "Git & GitHub", icon: "🐙" },
        { name: "CI/CD", icon: "🔄" },
        { name: "Vercel", icon: "▲" },
        { name: "Linux", icon: "🐧" }
      ]
    },
    {
      title: "Development Tools",
      icon: <Wrench className="w-8 h-8" />,
      gradient: "from-orange-500 to-red-500",
      skills: [
        { name: "VS Code", icon: "💻" },
        { name: "Figma", icon: "🎯" },
        { name: "Postman", icon: "📮" },
        { name: "Stripe", icon: "💳" },
        { name: "Socket.io", icon: "🔌" },
        { name: "Speech APIs", icon: "🎤" }
      ]
    },
    {
      title: "Certifications & Learning",
      icon: <Award className="w-8 h-8" />,
      gradient: "from-indigo-500 to-blue-500",
      skills: [
        { name: "KCNA (2025)", icon: "⎈" },
        { name: "AWS Cloud Practitioner", icon: "☁️" },
        { name: "Google IT Support", icon: "🔧" },
        { name: "JavaScript Algorithms", icon: "🧮" }
      ]
    }
  ];

  const achievements = [
    { number: "5+", label: "Years Coding", icon: "💻", color: "text-blue-600" },
    { number: "15+", label: "Projects Built", icon: "🚀", color: "text-green-600" },
    { number: "5", label: "Certifications", icon: "🏆", color: "text-purple-600" },
    { number: "100+", label: "Students Mentored", icon: "👥", color: "text-orange-600" }
  ];

  return (
    <section className="py-32 bg-gradient-to-br from-background via-accent/5 to-primary/5 relative overflow-hidden">
      {/* Premium Background System */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>
        <div className="absolute top-1/3 right-1/3 w-96 h-96 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/3 left-1/3 w-80 h-80 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '3s' }}></div>
      </div>

      <div className="container px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Revolutionary Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-accent/10 text-accent px-6 py-3 rounded-full text-sm font-medium mb-8 animate-fade-in-up">
              <span className="w-2 h-2 bg-accent rounded-full animate-pulse"></span>
              Technical Expertise
            </div>

            <h2 className="text-5xl md:text-7xl font-display font-bold mb-8 bg-gradient-to-r from-accent via-primary to-accent bg-clip-text text-transparent animate-gradient-shift bg-[length:400%_400%]">
              Technical Arsenal
            </h2>

            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-light">
              A comprehensive collection of modern technologies, frameworks, and certifications
              that power innovative, scalable, and user-centric applications.
            </p>
          </div>

          {/* Skills Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {skillCategories.map((category, index) => (
              <SkillCategory key={index} {...category} />
            ))}
          </div>

          {/* Achievement Highlights */}
          <div className="bg-card/30 backdrop-blur-sm rounded-2xl p-8 border shadow-sm">
            <h3 className="text-2xl font-bold text-center mb-8 text-primary">Professional Highlights</h3>
            <div className="grid md:grid-cols-4 gap-6">
              {achievements.map((achievement, index) => (
                <div key={index} className="text-center group hover:scale-105 transition-transform duration-300">
                  <div className="mb-4 text-4xl group-hover:scale-125 transition-transform duration-300">
                    {achievement.icon}
                  </div>
                  <div className={`text-4xl font-bold ${achievement.color} mb-2 group-hover:text-primary transition-colors`}>
                    {achievement.number}
                  </div>
                  <div className="text-sm text-muted-foreground font-medium">{achievement.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-12">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full font-semibold hover:scale-105 transition-transform duration-300 cursor-pointer shadow-xl">
              <Globe className="w-5 h-5" />
              Ready to Build Something Amazing Together
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
