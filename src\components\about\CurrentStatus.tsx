
import { Award, GraduationCap, Briefcase, Cloud } from "lucide-react";
import { useTranslation } from "react-i18next";

const CurrentStatus = () => {
  const { t } = useTranslation();
  
  return (
    <div className="bg-card/30 backdrop-blur-sm rounded-2xl p-8 border shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-center gap-3 mb-6">
        <Award className="w-6 h-6 text-purple-500" />
        <h3 className="text-2xl font-bold text-primary">{t("currentStatus")}</h3>
      </div>
      <div className="space-y-4">
        <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg border border-border/30 hover:border-primary/30 transition-colors">
          <GraduationCap className="w-5 h-5 text-indigo-500" />
          <div>
            <p className="font-medium">{t("finalYearProject")}</p>
            <p className="text-sm text-muted-foreground">{t("finalYearProjectDesc")}</p>
          </div>
        </div>
        <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg border border-border/30 hover:border-primary/30 transition-colors">
          <Briefcase className="w-5 h-5 text-green-500" />
          <div>
            <p className="font-medium">{t("activelySeekingOpportunities")}</p>
            <p className="text-sm text-muted-foreground">{t("activelySeekingOpportunitiesDesc")}</p>
          </div>
        </div>
        <div className="flex items-center gap-3 p-4 bg-background/50 rounded-lg border border-border/30 hover:border-primary/30 transition-colors">
          <Cloud className="w-5 h-5 text-blue-500" />
          <div>
            <p className="font-medium">{t("cloudCertifiedProfessional")}</p>
            <p className="text-sm text-muted-foreground">{t("cloudCertifiedProfessionalDesc")}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CurrentStatus;
