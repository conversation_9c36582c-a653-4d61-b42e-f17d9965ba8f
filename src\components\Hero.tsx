
import { <PERSON>D<PERSON>, <PERSON>ithub, <PERSON>edin, Mail, Calendar, Sparkles, Code2, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import HeroStats from "./hero/HeroStats";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";

const Hero = () => {
  const { t } = useTranslation();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const scrollToProjects = () => {
    document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToContact = () => {
    document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-background via-background to-primary/5">
      {/* Dynamic Background System */}
      <div className="absolute inset-0">
        {/* Animated Grid */}
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>

        {/* Interactive Gradient Orbs */}
        <div
          className="absolute w-96 h-96 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-3xl animate-float"
          style={{
            left: `${20 + mousePosition.x * 0.02}%`,
            top: `${10 + mousePosition.y * 0.02}%`,
          }}
        ></div>
        <div
          className="absolute w-80 h-80 bg-gradient-to-r from-accent/15 to-primary/15 rounded-full blur-3xl animate-float"
          style={{
            right: `${15 + mousePosition.x * 0.015}%`,
            bottom: `${15 + mousePosition.y * 0.015}%`,
            animationDelay: '2s'
          }}
        ></div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-primary/30 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`
              }}
            ></div>
          ))}
        </div>
      </div>

      <div className="container px-4 py-20 text-center relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Revolutionary Profile Section */}
          <div className={`mb-12 relative inline-block group transition-all duration-1000 ${isLoaded ? 'animate-fade-in-up' : 'opacity-0'}`}>
            {/* Glowing Ring */}
            <div className="absolute -inset-4 bg-gradient-to-r from-primary via-accent to-primary rounded-full opacity-75 group-hover:opacity-100 animate-gradient-shift bg-[length:400%_400%] blur-sm"></div>

            {/* Main Avatar */}
            <div className="relative w-40 h-40 mx-auto rounded-full bg-gradient-to-br from-primary via-accent to-primary p-1 shadow-2xl group-hover:scale-110 transition-all duration-500 animate-glow">
              <div className="w-full h-full rounded-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 overflow-hidden shadow-inner">
                <img
                  src="/headshot-placeholder.svg"
                  alt="Abdulai Yorli Iddrisu - Professional Headshot"
                  className="w-full h-full object-cover object-center"
                  onError={(e) => {
                    // Fallback to initials if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `
                        <div class="w-full h-full flex items-center justify-center text-5xl font-bold bg-gradient-to-br from-primary to-accent bg-clip-text text-transparent">
                          AY
                        </div>
                      `;
                    }
                  }}
                />
              </div>
            </div>

            {/* Status Indicators */}
            <div className="absolute -bottom-2 -right-2 bg-green-500 w-12 h-12 rounded-full border-4 border-background flex items-center justify-center shadow-lg animate-bounce-gentle">
              <Sparkles className="text-white w-6 h-6" />
            </div>

            {/* Floating Icons */}
            <div className="absolute -top-4 -left-4 bg-primary/10 backdrop-blur-sm w-10 h-10 rounded-full border border-primary/20 flex items-center justify-center animate-float">
              <Code2 className="text-primary w-5 h-5" />
            </div>
            <div className="absolute -top-4 -right-4 bg-accent/10 backdrop-blur-sm w-10 h-10 rounded-full border border-accent/20 flex items-center justify-center animate-float" style={{ animationDelay: '1s' }}>
              <Zap className="text-accent w-5 h-5" />
            </div>
          </div>

          {/* Revolutionary Typography */}
          <div className={`mb-12 transition-all duration-1000 delay-300 ${isLoaded ? 'animate-fade-in-up' : 'opacity-0'}`}>
            <h1 className="text-6xl md:text-8xl lg:text-9xl font-display font-bold mb-8 bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent animate-gradient-shift bg-[length:400%_400%] leading-tight">
              {t("greeting")}
            </h1>

            <div className="space-y-6 mb-8">
              <p className="text-2xl md:text-3xl text-muted-foreground max-w-5xl mx-auto leading-relaxed font-light">
                {t("subtitle")}
              </p>

              {/* Premium Status Badges */}
              <div className="flex flex-wrap justify-center gap-6 mt-8">
                <div className="group relative">
                  <div className="absolute -inset-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000"></div>
                  <div className="relative inline-flex items-center gap-3 bg-background border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-6 py-3 rounded-full text-base font-medium">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    {t("availableForHire")}
                  </div>
                </div>

                <div className="group relative">
                  <div className="absolute -inset-1 bg-gradient-to-r from-primary to-accent rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000"></div>
                  <div className="relative inline-flex items-center gap-3 bg-background border border-primary/20 text-primary px-6 py-3 rounded-full text-base font-medium">
                    <Calendar className="w-5 h-5" />
                    {t("graduatingAugust")}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <HeroStats />

          {/* CTA Buttons with enhanced styling */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-10">
            <Button
              onClick={scrollToProjects}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-full font-semibold transition-all hover:scale-105 shadow-xl hover:shadow-2xl text-lg"
            >
              {t("exploreWork")}
            </Button>
            <Button
              onClick={scrollToContact}
              variant="outline"
              size="lg"
              className="px-10 py-4 rounded-full font-semibold transition-all hover:scale-105 border-2 hover:border-primary hover:bg-primary hover:text-primary-foreground text-lg shadow-xl"
            >
              {t("getResume")}
            </Button>
          </div>

          {/* Enhanced Social Links */}
          <div className="flex justify-center gap-8 mb-12">
            <a
              href="https://github.com/yorliabdulai"
              target="_blank"
              rel="noopener noreferrer"
              className="p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all hover:scale-125 transform hover:shadow-lg hover:bg-card"
            >
              <Github size={28} />
            </a>
            <a
              href="https://linkedin.com/in/abdulai-yorli-iddrisu"
              target="_blank"
              rel="noopener noreferrer"
              className="p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all hover:scale-125 transform hover:shadow-lg hover:bg-card"
            >
              <Linkedin size={28} />
            </a>
            <a
              href="mailto:<EMAIL>"
              className="p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all hover:scale-125 transform hover:shadow-lg hover:bg-card"
            >
              <Mail size={28} />
            </a>
          </div>

          {/* Enhanced scroll indicator */}
          <div className="animate-bounce">
            <div className="p-2 bg-card/50 backdrop-blur-sm border rounded-full inline-block">
              <ArrowDown className="text-muted-foreground" size={24} />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
