
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mail, Calendar } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import HeroStats from "./hero/HeroStats";
import { useTranslation } from "react-i18next";

const Hero = () => {
  const { t } = useTranslation();
  
  const scrollToProjects = () => {
    document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToContact = () => {
    document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-blue-50/30 dark:to-blue-950/20 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-20 right-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      
      <div className="container px-4 py-20 text-center relative z-10">
        <div className="max-w-5xl mx-auto">
          {/* Enhanced Profile Image */}
          <div className="mb-8 relative inline-block group">
            <div className="w-36 h-36 mx-auto rounded-full bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 p-1 shadow-2xl group-hover:scale-105 transition-transform duration-300">
              <div className="w-full h-full rounded-full bg-muted flex items-center justify-center text-4xl font-bold text-primary shadow-inner">
                AY
              </div>
            </div>
            <div className="absolute -bottom-2 -right-2 bg-green-500 w-10 h-10 rounded-full border-4 border-background flex items-center justify-center shadow-lg animate-pulse">
              <span className="text-white text-xs font-bold">✓</span>
            </div>
            <div className="absolute -top-2 -left-2 bg-blue-500 w-8 h-8 rounded-full border-4 border-background flex items-center justify-center shadow-lg">
              <span className="text-white text-xs">⎈</span>
            </div>
          </div>

          {/* Main heading with enhanced animation */}
          <div className="mb-8 animate-fade-in">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-primary via-blue-600 to-purple-600 bg-clip-text text-transparent">
              {t("greeting")}
            </h1>
            <div className="space-y-2 mb-6">
              <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                {t("subtitle")}
              </p>
              <div className="flex flex-wrap justify-center gap-4 mt-4">
                <div className="inline-flex items-center gap-2 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 px-4 py-2 rounded-full text-sm font-medium animate-pulse">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  {t("availableForHire")}
                </div>
                <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 px-4 py-2 rounded-full text-sm font-medium">
                  <Calendar className="w-4 h-4" />
                  {t("graduatingAugust")}
                </div>
              </div>
            </div>
          </div>

          <HeroStats />

          {/* CTA Buttons with enhanced styling */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-10">
            <Button 
              onClick={scrollToProjects} 
              size="lg" 
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-full font-semibold transition-all hover:scale-105 shadow-xl hover:shadow-2xl text-lg"
            >
              {t("exploreWork")}
            </Button>
            <Button 
              onClick={scrollToContact} 
              variant="outline" 
              size="lg" 
              className="px-10 py-4 rounded-full font-semibold transition-all hover:scale-105 border-2 hover:border-primary hover:bg-primary hover:text-primary-foreground text-lg shadow-xl"
            >
              {t("getResume")}
            </Button>
          </div>

          {/* Enhanced Social Links */}
          <div className="flex justify-center gap-8 mb-12">
            <a 
              href="https://github.com/yorliabdulai" 
              target="_blank" 
              rel="noopener noreferrer" 
              className="p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all hover:scale-125 transform hover:shadow-lg hover:bg-card"
            >
              <Github size={28} />
            </a>
            <a 
              href="https://linkedin.com/in/abdulai-yorli-iddrisu" 
              target="_blank" 
              rel="noopener noreferrer" 
              className="p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all hover:scale-125 transform hover:shadow-lg hover:bg-card"
            >
              <Linkedin size={28} />
            </a>
            <a 
              href="mailto:<EMAIL>" 
              className="p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all hover:scale-125 transform hover:shadow-lg hover:bg-card"
            >
              <Mail size={28} />
            </a>
          </div>

          {/* Enhanced scroll indicator */}
          <div className="animate-bounce">
            <div className="p-2 bg-card/50 backdrop-blur-sm border rounded-full inline-block">
              <ArrowDown className="text-muted-foreground" size={24} />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
