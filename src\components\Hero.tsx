
import { <PERSON><PERSON><PERSON>, <PERSON>ithub, Linkedin, Mail, Calendar, Sparkles, Code2, Zap, Download } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import HeroStats from "./hero/HeroStats";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";

const Hero = () => {
  const { t } = useTranslation();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const scrollToProjects = () => {
    document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToContact = () => {
    document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
  };

  const downloadCV = () => {
    const link = document.createElement('a');
    link.href = '/AbdulaiYorliIddrisuCV.pdf';
    link.download = 'Abdulai_Yorli_Iddrisu_CV.pdf';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-background via-background to-primary/5">
      {/* Dynamic Background System */}
      <div className="absolute inset-0">
        {/* Animated Grid */}
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>

        {/* Interactive Gradient Orbs */}
        <div
          className="absolute w-96 h-96 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-3xl animate-float"
          style={{
            left: `${20 + mousePosition.x * 0.02}%`,
            top: `${10 + mousePosition.y * 0.02}%`,
          }}
        ></div>
        <div
          className="absolute w-80 h-80 bg-gradient-to-r from-accent/15 to-primary/15 rounded-full blur-3xl animate-float"
          style={{
            right: `${15 + mousePosition.x * 0.015}%`,
            bottom: `${15 + mousePosition.y * 0.015}%`,
            animationDelay: '2s'
          }}
        ></div>

        {/* Advanced Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className={`absolute rounded-full animate-parallax-float ${
                i % 3 === 0 ? 'w-2 h-2 bg-primary/20' :
                i % 3 === 1 ? 'w-1 h-1 bg-accent/30' :
                'w-1.5 h-1.5 bg-gradient-to-r from-primary/20 to-accent/20'
              }`}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 6}s`,
                animationDuration: `${4 + Math.random() * 6}s`
              }}
            ></div>
          ))}
        </div>

        {/* Morphing Background Shapes */}
        <div className="absolute top-1/4 left-1/3 w-64 h-64 bg-gradient-to-r from-primary/5 to-accent/5 animate-liquid-morph"></div>
        <div className="absolute bottom-1/3 right-1/4 w-48 h-48 bg-gradient-to-l from-accent/5 to-primary/5 animate-morphing"></div>
      </div>

      <div className="container px-4 py-20 text-center relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Revolutionary Profile Section */}
          <div className={`mb-12 relative inline-block group transition-all duration-1000 magnetic-hover hover-glow ${isLoaded ? 'animate-bounce-in-down' : 'opacity-0'}`}>
            {/* Advanced Glowing Ring */}
            <div className="absolute -inset-6 bg-gradient-to-r from-primary via-accent to-primary rounded-full opacity-60 group-hover:opacity-100 animate-gradient-shift bg-[length:400%_400%] blur-lg transition-all duration-700"></div>
            <div className="absolute -inset-4 bg-gradient-to-r from-accent via-primary to-accent rounded-full opacity-40 group-hover:opacity-80 animate-gradient-shift bg-[length:300%_300%] blur-md transition-all duration-500" style={{ animationDelay: '0.5s' }}></div>

            {/* Main Avatar with Advanced Effects */}
            <div className="relative w-40 h-40 mx-auto rounded-full bg-gradient-to-br from-primary via-accent to-primary p-1 shadow-2xl group-hover:scale-110 transition-all duration-700 animate-pulse-glow">
              <div className="w-full h-full rounded-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 overflow-hidden shadow-inner">
                <img
                  src="/yorli.jpg"
                  alt="Abdulai Yorli Iddrisu - Professional Headshot"
                  className="w-full h-full object-cover object-center"
                  onError={(e) => {
                    // Fallback to initials if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `
                        <div class="w-full h-full flex items-center justify-center text-5xl font-bold bg-gradient-to-br from-primary to-accent bg-clip-text text-transparent">
                          AY
                        </div>
                      `;
                    }
                  }}
                />
              </div>
            </div>

            {/* Status Indicators */}
            <div className="absolute -bottom-2 -right-2 bg-green-500 w-12 h-12 rounded-full border-4 border-background flex items-center justify-center shadow-lg animate-bounce-gentle">
              <Sparkles className="text-white w-6 h-6" />
            </div>

            {/* Floating Icons */}
            <div className="absolute -top-4 -left-4 bg-primary/10 backdrop-blur-sm w-10 h-10 rounded-full border border-primary/20 flex items-center justify-center animate-float">
              <Code2 className="text-primary w-5 h-5" />
            </div>
            <div className="absolute -top-4 -right-4 bg-accent/10 backdrop-blur-sm w-10 h-10 rounded-full border border-accent/20 flex items-center justify-center animate-float" style={{ animationDelay: '1s' }}>
              <Zap className="text-accent w-5 h-5" />
            </div>
          </div>

          {/* Revolutionary Typography */}
          <div className={`mb-12 transition-all duration-1000 delay-300 ${isLoaded ? 'animate-slide-in-left' : 'opacity-0'}`}>
            <h1 className="text-6xl md:text-8xl lg:text-9xl font-display font-bold mb-8 bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent animate-gradient-shift bg-[length:400%_400%] leading-tight hover:animate-text-shimmer transition-all duration-500 cursor-default">
              {t("greeting")}
            </h1>

            <div className="space-y-6 mb-8">
              <p className="text-2xl md:text-3xl text-muted-foreground max-w-5xl mx-auto leading-relaxed font-light">
                {t("subtitle")}
              </p>

              {/* Premium Status Badges */}
              <div className="flex flex-wrap justify-center gap-6 mt-8">
                <div className="group relative">
                  <div className="absolute -inset-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000"></div>
                  <div className="relative inline-flex items-center gap-3 bg-background border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400 px-6 py-3 rounded-full text-base font-medium">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    {t("availableForHire")}
                  </div>
                </div>

                <div className="group relative">
                  <div className="absolute -inset-1 bg-gradient-to-r from-primary to-accent rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000"></div>
                  <div className="relative inline-flex items-center gap-3 bg-background border border-primary/20 text-primary px-6 py-3 rounded-full text-base font-medium">
                    <Calendar className="w-5 h-5" />
                    {t("graduatingAugust")}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <HeroStats />

          {/* CTA Buttons with enhanced styling */}
          <div className={`flex flex-col sm:flex-row gap-4 justify-center mb-10 ${isLoaded ? 'animate-slide-in-bottom animate-delay-600' : 'opacity-0'}`}>
            <Button
              onClick={scrollToProjects}
              size="lg"
              className="magnetic-hover hover-glow bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 rounded-full font-semibold transition-all duration-500 hover:scale-110 shadow-xl hover:shadow-2xl text-lg relative overflow-hidden group"
            >
              <span className="relative z-10">{t("exploreWork")}</span>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </Button>
            <Button
              onClick={downloadCV}
              variant="outline"
              size="lg"
              className="magnetic-hover hover-glow px-10 py-4 rounded-full font-semibold transition-all duration-500 hover:scale-110 border-2 hover:border-primary hover:bg-primary hover:text-primary-foreground text-lg shadow-xl relative overflow-hidden group"
            >
              <span className="relative z-10 flex items-center gap-2">
                <Download className="w-5 h-5" />
                {t("downloadCV")}
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-accent opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
            </Button>
          </div>

          {/* Enhanced Social Links */}
          <div className={`flex justify-center gap-8 mb-12 ${isLoaded ? 'animate-slide-in-right animate-delay-800' : 'opacity-0'}`}>
            <a
              href="https://github.com/yorliabdulai"
              target="_blank"
              rel="noopener noreferrer"
              className="magnetic-hover hover-glow p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all duration-500 hover:scale-125 transform hover:shadow-lg hover:bg-card animate-rotate-in animate-delay-100 relative overflow-hidden group"
            >
              <Github size={28} className="relative z-10" />
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full"></div>
            </a>
            <a
              href="https://linkedin.com/in/abdulai-yorli-iddrisu"
              target="_blank"
              rel="noopener noreferrer"
              className="magnetic-hover hover-glow p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all duration-500 hover:scale-125 transform hover:shadow-lg hover:bg-card animate-rotate-in animate-delay-200 relative overflow-hidden group"
            >
              <Linkedin size={28} className="relative z-10" />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-blue-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full"></div>
            </a>
            <a
              href="mailto:<EMAIL>"
              className="magnetic-hover hover-glow p-4 bg-card/50 backdrop-blur-sm border rounded-full text-muted-foreground hover:text-primary transition-all duration-500 hover:scale-125 transform hover:shadow-lg hover:bg-card animate-rotate-in animate-delay-300 relative overflow-hidden group"
            >
              <Mail size={28} className="relative z-10" />
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full"></div>
            </a>
          </div>

          {/* Enhanced scroll indicator */}
          <div className="animate-bounce">
            <div className="p-2 bg-card/50 backdrop-blur-sm border rounded-full inline-block">
              <ArrowDown className="text-muted-foreground" size={24} />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
