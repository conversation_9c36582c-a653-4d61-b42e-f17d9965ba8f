
import { MapPin, Award, Briefcase } from "lucide-react";
import { useTranslation } from "react-i18next";

const AboutHero = () => {
  const { t } = useTranslation();
  
  return (
    <div className="text-center mb-16">
      <div className="inline-flex items-center gap-2 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 px-4 py-2 rounded-full text-sm font-medium mb-6 animate-fade-in">
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        {t("availableForRoles")}
      </div>
      
      <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
        {t("aboutTitle")}
      </h2>
      
      <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-8">
        {t("aboutDescription")}
      </p>

      <div className="flex flex-wrap justify-center gap-6 max-w-3xl mx-auto">
        <div className="flex items-center gap-2 px-4 py-2 bg-card/50 backdrop-blur-sm rounded-full border">
          <MapPin className="w-4 h-4 text-blue-500" />
          <span className="text-sm font-medium">{t("location")}</span>
        </div>
        <div className="flex items-center gap-2 px-4 py-2 bg-card/50 backdrop-blur-sm rounded-full border">
          <Award className="w-4 h-4 text-purple-500" />
          <span className="text-sm font-medium">{t("certifiedBadge")}</span>
        </div>
        <div className="flex items-center gap-2 px-4 py-2 bg-card/50 backdrop-blur-sm rounded-full border">
          <Briefcase className="w-4 h-4 text-green-500" />
          <span className="text-sm font-medium">{t("internshipsBadge")}</span>
        </div>
      </div>
    </div>
  );
};

export default AboutHero;
