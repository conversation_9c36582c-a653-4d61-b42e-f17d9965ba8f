
import { Code, Cloud, Database, Users } from "lucide-react";
import { useTranslation } from "react-i18next";

const ExpertiseGrid = () => {
  const { t } = useTranslation();
  
  const expertiseAreas = [
    {
      title: t("frontendArchitecture"),
      description: t("frontendArchitectureDesc"),
      icon: <Code className="w-8 h-8 text-blue-500" />,
      skills: [t("componentArchitecture"), t("stateManagement"), t("performanceOptimization"), t("designSystems")],
      gradient: "from-blue-500/10 to-cyan-500/10"
    },
    {
      title: t("cloudNativeDevelopment"),
      description: t("cloudNativeDevelopmentDesc"),
      icon: <Cloud className="w-8 h-8 text-purple-500" />,
      skills: [t("awsServices"), t("kubernetes"), t("serverless"), t("cicdPipelines")],
      gradient: "from-purple-500/10 to-violet-500/10"
    },
    {
      title: t("fullStackIntegration"),
      description: t("fullStackIntegrationDesc"),
      icon: <Database className="w-8 h-8 text-green-500" />,
      skills: [t("apiDesign"), t("databaseModeling"), t("authentication"), t("realtimeFeatures")],
      gradient: "from-green-500/10 to-emerald-500/10"
    },
    {
      title: t("developerExperience"),
      description: t("developerExperienceDesc"),
      icon: <Users className="w-8 h-8 text-orange-500" />,
      skills: [t("developerTools"), t("codeQuality"), t("teamLeadership"), t("mentoring")],
      gradient: "from-orange-500/10 to-red-500/10"
    }
  ];

  return (
    <div className="mb-16">
      <h3 className="text-3xl font-bold text-center mb-12 text-primary">{t("areasOfExpertise")}</h3>
      <div className="grid md:grid-cols-2 gap-8">
        {expertiseAreas.map((area, index) => (
          <div 
            key={index} 
            className={`group bg-gradient-to-br ${area.gradient} border border-border/50 backdrop-blur-sm rounded-2xl p-8 hover:shadow-xl transition-all duration-500 hover:-translate-y-2`}
          >
            <div className="flex items-start gap-4 mb-6">
              <div className="p-3 bg-background/80 backdrop-blur-sm rounded-xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                {area.icon}
              </div>
              <div className="flex-1">
                <h4 className="text-xl font-bold mb-2 text-primary">{area.title}</h4>
                <p className="text-muted-foreground leading-relaxed text-sm">{area.description}</p>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {area.skills.map((skill, skillIndex) => (
                <span 
                  key={skillIndex} 
                  className="px-3 py-1 bg-background/60 backdrop-blur-sm text-sm rounded-full hover:bg-primary hover:text-primary-foreground transition-colors cursor-default border border-border/30"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExpertiseGrid;
