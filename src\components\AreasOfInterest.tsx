import { GraduationCap, Award, Briefcase, Code, Brain, Rocket } from "lucide-react";
import { useTranslation } from "react-i18next";

const AreasOfInterest = () => {
  const { t } = useTranslation();

  const careerData = {
    education: [
      {
        year: "2021-2022",
        title: "Diploma in Computer Science",
        organization: "CK Tedam University"
      },
      {
        year: "2023-2025",
        title: "BSc Computer Science",
        organization: "University for Development Studies"
      }
    ],
    certifications: [
      {
        year: "2024",
        title: "AWS re/Start Program",
        organization: "Amazon Web Services"
      },
      {
        year: "2024",
        title: "AWS Cloud Practitioner Certification",
        organization: ""
      },
      {
        year: "2025",
        title: "KCNA Certification (Planned)",
        organization: "Cloud Native Computing Foundation"
      }
    ],
    experience: [
      {
        year: "2024",
        title: "Software Development Intern",
        organization: "Amalitech"
      },
      {
        year: "2024",
        title: "Technical Intern",
        organization: "Wikimedia Deutschland"
      },
      {
        year: "2024",
        title: "IT Intern",
        organization: "Agricultural Development Bank"
      }
    ]
  };

  const interests = [
    {
      icon: <Code className="w-6 h-6" />,
      title: "Full-Stack Development",
      description: "Building scalable web applications with modern frameworks and cloud technologies"
    },
    {
      icon: <Brain className="w-6 h-6" />,
      title: "Cloud Architecture",
      description: "Designing and implementing cloud-native solutions with AWS, GCP, and Kubernetes"
    },
    {
      icon: <Rocket className="w-6 h-6" />,
      title: "Web3 & Blockchain",
      description: "Developing decentralized applications and smart contracts for the future of web"
    }
  ];

  return (
    <section className="py-32 bg-gradient-to-br from-background via-accent/5 to-primary/5 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>
        <div className="absolute top-1/3 right-1/4 w-96 h-96 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl animate-float"></div>
      </div>

      <div className="container px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-accent/10 text-accent px-6 py-3 rounded-full text-sm font-medium mb-8 animate-fade-in-up">
              <Brain className="w-4 h-4" />
              Professional Journey
            </div>

            <h2 className="text-5xl md:text-7xl font-display font-bold mb-8 bg-gradient-to-r from-accent via-primary to-accent bg-clip-text text-transparent animate-gradient-shift bg-[length:400%_400%]">
              Areas of Interest
            </h2>

            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-light">
              My passion areas and professional development journey
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16">
            {/* Career Timeline */}
            <div className="space-y-12">
              {/* Education */}
              <div className="animate-fade-in-left">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg">
                    <GraduationCap className="w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground">Education</h3>
                </div>
                <div className="space-y-4 ml-4">
                  {careerData.education.map((item, index) => (
                    <div key={index} className="border-l-2 border-blue-200 dark:border-blue-800 pl-6 pb-4">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <span className="font-bold text-blue-600 dark:text-blue-400 text-sm">
                          {item.year}:
                        </span>
                        <span className="font-semibold text-foreground">
                          {item.title}
                        </span>
                      </div>
                      <p className="text-muted-foreground text-sm mt-1">
                        {item.organization}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Training & Certifications */}
              <div className="animate-fade-in-left" style={{ animationDelay: '0.2s' }}>
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-green-600 text-white shadow-lg">
                    <Award className="w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground">Training & Certifications</h3>
                </div>
                <div className="space-y-4 ml-4">
                  {careerData.certifications.map((item, index) => (
                    <div key={index} className="border-l-2 border-green-200 dark:border-green-800 pl-6 pb-4">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <span className="font-bold text-green-600 dark:text-green-400 text-sm">
                          {item.year}:
                        </span>
                        <span className="font-semibold text-foreground">
                          {item.title}
                        </span>
                      </div>
                      {item.organization && (
                        <p className="text-muted-foreground text-sm mt-1">
                          {item.organization}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Professional Experience */}
              <div className="animate-fade-in-left" style={{ animationDelay: '0.4s' }}>
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-lg">
                    <Briefcase className="w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground">Professional Experience</h3>
                </div>
                <div className="space-y-4 ml-4">
                  {careerData.experience.map((item, index) => (
                    <div key={index} className="border-l-2 border-purple-200 dark:border-purple-800 pl-6 pb-4">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <span className="font-bold text-purple-600 dark:text-purple-400 text-sm">
                          {item.year}:
                        </span>
                        <span className="font-semibold text-foreground">
                          {item.title}
                        </span>
                      </div>
                      <p className="text-muted-foreground text-sm mt-1">
                        {item.organization}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Areas of Interest */}
            <div className="space-y-8">
              <div className="animate-fade-in-right">
                <h3 className="text-3xl font-bold text-foreground mb-8">Core Interests</h3>
                <div className="space-y-6">
                  {interests.map((interest, index) => (
                    <div 
                      key={index} 
                      className="bg-card/50 backdrop-blur-sm rounded-2xl p-6 border shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-1 group"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <div className="flex items-start gap-4">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-accent text-white group-hover:scale-110 transition-transform duration-300 shadow-lg">
                          {interest.icon}
                        </div>
                        <div className="flex-1">
                          <h4 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors">
                            {interest.title}
                          </h4>
                          <p className="text-muted-foreground leading-relaxed">
                            {interest.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AreasOfInterest;
