
import { useTranslation } from "react-i18next";

const HeroStats = () => {
  const { t } = useTranslation();
  
  const stats = [
    { icon: "🚀", value: "15+", label: t("projectsBuilt"), color: "text-blue-600" },
    { icon: "💼", value: "3", label: t("internships"), color: "text-green-600" },
    { icon: "🏆", value: "5", label: t("certifications"), color: "text-purple-600" },
    { icon: "👥", value: "100+", label: t("studentsMentored"), color: "text-orange-600" }
  ];

  return (
    <div className="grid md:grid-cols-4 gap-4 mb-10 max-w-4xl mx-auto">
      {stats.map((stat, index) => (
        <div 
          key={index}
          className="bg-card/70 backdrop-blur-sm border rounded-2xl p-6 hover:scale-105 transition-all duration-300 hover:shadow-lg group"
        >
          <div className="text-3xl mb-2 group-hover:scale-110 transition-transform">{stat.icon}</div>
          <div className={`text-2xl font-bold ${stat.color} mb-1`}>{stat.value}</div>
          <div className="text-sm text-muted-foreground font-medium">{stat.label}</div>
        </div>
      ))}
    </div>
  );
};

export default HeroStats;
