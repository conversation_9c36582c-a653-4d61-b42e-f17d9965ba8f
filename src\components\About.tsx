
import AboutHero from "./about/AboutHero";
import ExpertiseGrid from "./about/ExpertiseGrid";
import CareerTimeline from "./about/CareerTimeline";
import CurrentStatus from "./about/CurrentStatus";

const About = () => {
  return (
    <section id="about" className="py-20 bg-gradient-to-br from-muted/30 via-background to-muted/20 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-20 right-20 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-20 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
      
      <div className="container px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <AboutHero />
          <ExpertiseGrid />
          
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            <CurrentStatus />
            <CareerTimeline />
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
