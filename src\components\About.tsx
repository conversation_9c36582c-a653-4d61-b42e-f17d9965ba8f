
import AboutHero from "./about/AboutHero";
import ExpertiseGrid from "./about/ExpertiseGrid";
import CareerTimeline from "./about/CareerTimeline";
import CurrentStatus from "./about/CurrentStatus";

const About = () => {
  return (
    <section id="about" className="py-32 bg-gradient-to-br from-primary/5 via-background to-accent/5 relative overflow-hidden">
      {/* Premium Background System */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05]"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-accent/10 to-primary/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      <div className="container px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <AboutHero />
          <ExpertiseGrid />

          <div className="grid lg:grid-cols-2 gap-16 items-start mt-20">
            <div className="animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <CurrentStatus />
            </div>
            <div className="animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <CareerTimeline />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
