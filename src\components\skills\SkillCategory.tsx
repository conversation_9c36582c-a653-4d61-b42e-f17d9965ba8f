
interface SkillCategoryProps {
  title: string;
  icon: React.ReactNode;
  gradient: string;
  skills: Array<{ name: string; icon: string }>;
}

const SkillCategory = ({ title, icon, gradient, skills }: SkillCategoryProps) => {
  return (
    <div className="group bg-card rounded-2xl p-8 border shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
      <div className="flex items-center gap-4 mb-8">
        <div className={`p-3 rounded-xl bg-gradient-to-br ${gradient} text-white group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
          {icon}
        </div>
        <h3 className="text-xl font-bold text-primary">{title}</h3>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        {skills.map((skill, skillIndex) => (
          <div 
            key={skillIndex} 
            className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg hover:bg-primary hover:text-primary-foreground transition-all duration-300 cursor-default group/skill border border-border/30"
          >
            <span className="text-lg group-hover/skill:scale-125 transition-transform">
              {skill.icon}
            </span>
            <span className="text-sm font-medium">{skill.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SkillCategory;
