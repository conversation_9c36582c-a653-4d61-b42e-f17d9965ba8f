
interface SkillCategoryProps {
  title: string;
  icon: React.ReactNode;
  gradient: string;
  skills: Array<{ name: string; icon: React.ReactNode }>;
}

const SkillCategory = ({ title, icon, gradient, skills }: SkillCategoryProps) => {
  return (
    <div className="group bg-card/50 backdrop-blur-sm rounded-2xl p-8 border shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-2 hover-lift animate-fade-in-up">
      <div className="flex items-center gap-4 mb-8">
        <div className={`p-3 rounded-xl bg-gradient-to-br ${gradient} text-white group-hover:scale-110 transition-transform duration-300 shadow-lg animate-pulse-glow`}>
          {icon}
        </div>
        <h3 className="text-xl font-bold text-primary group-hover:text-accent transition-colors duration-300">{title}</h3>
      </div>

      <div className="grid grid-cols-2 gap-3">
        {skills.map((skill, skillIndex) => (
          <div
            key={skillIndex}
            className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg hover:bg-gradient-to-r hover:from-primary hover:to-accent hover:text-white transition-all duration-300 cursor-default group/skill border border-border/30 hover:border-primary/50 hover:scale-105"
            style={{ animationDelay: `${skillIndex * 0.1}s` }}
          >
            <div className="group-hover/skill:scale-125 transition-transform duration-300">
              {skill.icon}
            </div>
            <span className="text-sm font-medium">{skill.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SkillCategory;
