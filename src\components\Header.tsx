
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, Download } from "lucide-react";
import { ThemeToggle } from "@/components/ThemeToggle";
import { LanguageToggle } from "@/components/LanguageToggle";
import { useTranslation } from "react-i18next";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
    setIsMenuOpen(false);
  };

  const scrollToHome = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setIsMenuOpen(false);
  };

  const downloadCV = () => {
    const link = document.createElement('a');
    link.href = '/AbdulaiYorliIddrisuCV.pdf';
    link.download = 'Abdulai_Yorli_Iddrisu_CV.pdf';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const navItems = [
    { name: t("about"), id: "about" },
    { name: t("projects"), id: "projects" },
    { name: t("contact"), id: "contact" }
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
      isScrolled
        ? 'bg-background/80 backdrop-blur-xl border-b border-border/50 shadow-xl'
        : 'bg-transparent'
    }`}>
      <nav className="container px-6 py-6">
        <div className="flex items-center justify-between">
          {/* Premium Logo */}
          <button
            onClick={scrollToHome}
            className="group relative font-display font-bold text-2xl cursor-pointer"
          >
            <div className="absolute -inset-2 bg-gradient-to-r from-primary to-accent rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur"></div>
            <span className="relative bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent group-hover:scale-105 transition-transform duration-300">
              Abdulai Yorli
            </span>
          </button>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-6">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => scrollToSection(item.id)}
                className="text-muted-foreground hover:text-primary transition-colors font-medium"
              >
                {item.name}
              </button>
            ))}

            {/* Theme and Language toggles */}
            <div className="flex items-center gap-2">
              <ThemeToggle />
              <LanguageToggle />
            </div>

            <Button
              onClick={downloadCV}
              variant="outline"
              className="flex items-center gap-2 hover:bg-primary hover:text-primary-foreground"
            >
              <Download size={16} />
              {t("downloadCV")}
            </Button>

            <Button
              onClick={() => scrollToSection('contact')}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
            >
              {t("letsTalk")}
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center gap-2">
            <ThemeToggle />
            <LanguageToggle />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 pb-4 border-t bg-background/95 backdrop-blur-sm">
            <div className="flex flex-col gap-4 pt-4">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className="text-left text-muted-foreground hover:text-primary transition-colors font-medium"
                >
                  {item.name}
                </button>
              ))}
              <Button
                onClick={downloadCV}
                variant="outline"
                className="mt-2 flex items-center gap-2 justify-center hover:bg-primary hover:text-primary-foreground"
              >
                <Download size={16} />
                {t("downloadCV")}
              </Button>
              <Button
                onClick={() => scrollToSection('contact')}
                className="mt-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
              >
                {t("letsTalk")}
              </Button>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
};

export default Header;
