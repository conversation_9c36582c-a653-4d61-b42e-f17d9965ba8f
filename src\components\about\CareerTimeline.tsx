
import { Calendar, Cloud, Users, Briefcase, GraduationCap } from "lucide-react";
import { useTranslation } from "react-i18next";

const CareerTimeline = () => {
  const { t } = useTranslation();
  
  const timeline = [
    {
      year: "2025",
      event: "KCNA Certification (Planned)",
      location: "Cloud Native Computing Foundation",
      icon: <Cloud className="w-5 h-5 text-blue-500" />,
      type: "certification"
    },
    {
      year: "2024",
      event: "AWS Cloud Practitioner Certification",
      location: "Amazon Web Services",
      icon: <Cloud className="w-5 h-5 text-orange-500" />,
      type: "certification"
    },
    {
      year: "2024",
      event: "AWS re/Start Program",
      location: "Amazon Web Services",
      icon: <Cloud className="w-5 h-5 text-orange-500" />,
      type: "certification"
    },
    {
      year: "2024",
      event: "Software Development Intern",
      location: "Amalitech",
      icon: <Briefcase className="w-5 h-5 text-blue-600" />,
      type: "experience"
    },
    {
      year: "2024",
      event: "Technical Intern",
      location: "Wikimedia Deutschland",
      icon: <Briefcase className="w-5 h-5 text-gray-600" />,
      type: "experience"
    },
    {
      year: "2024",
      event: "IT Intern",
      location: "Agricultural Development Bank",
      icon: <Briefcase className="w-5 h-5 text-green-600" />,
      type: "experience"
    },
    {
      year: "2023-2025",
      event: "BSc Computer Science",
      location: "University for Development Studies",
      icon: <GraduationCap className="w-5 h-5 text-indigo-500" />,
      type: "education"
    },
    {
      year: "2021-2022",
      event: "Diploma in Computer Science",
      location: "CK Tedam University",
      icon: <GraduationCap className="w-5 h-5 text-indigo-500" />,
      type: "education"
    }
  ];

  return (
    <div className="bg-card/30 backdrop-blur-sm rounded-2xl p-8 border shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-center gap-3 mb-8">
        <Calendar className="w-6 h-6 text-orange-500" />
        <h3 className="text-2xl font-bold text-primary">{t("careerTimeline")}</h3>
      </div>
      <div className="space-y-6 max-h-96 overflow-y-auto">
        {timeline.map((item, index) => (
          <div key={index} className="flex gap-4 group relative">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-background border-2 border-border rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-md">
                {item.icon}
              </div>
              {index < timeline.length - 1 && (
                <div className="w-px h-16 bg-gradient-to-b from-border to-transparent mt-2"></div>
              )}
            </div>
            <div className="flex-1 pb-4">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-bold text-lg text-primary">{item.year}</span>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  item.type === 'certification' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                  item.type === 'leadership' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                  item.type === 'experience' ? 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400' :
                  'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400'
                }`}>
                  {t(item.type)}
                </span>
              </div>
              <h4 className="font-semibold text-base mb-1">{item.event}</h4>
              <p className="text-sm text-muted-foreground">{item.location}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CareerTimeline;
