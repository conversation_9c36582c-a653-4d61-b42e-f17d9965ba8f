
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

// Translation resources
const resources = {
  en: {
    translation: {
      // Navigation
      about: "About",
      projects: "Projects",
      contact: "Contact",
      letsTalk: "Let's Talk",

      // Hero section
      greeting: "Hi, I'm <PERSON><PERSON> 👋",
      subtitle: "Frontend Specialist | AWS & Kubernetes Certified | Building Modern Web Experiences",
      availableForHire: "Available for Hire",
      graduatingAugust: "Graduating August 2025",
      exploreWork: "🚀 Explore My Work",
      getResume: "📄 Get My Resume",
      downloadCV: "Download CV",

      // Stats
      projectsBuilt: "Projects Built",
      internships: "Industry Internships",
      certifications: "Certifications",
      studentsMentored: "Students Mentored",

      // About section
      aboutTitle: "About Me",
      availableForRoles: "Available for Frontend & Full-Stack Roles",
      aboutDescription: "A passionate frontend specialist and cloud practitioner from Ghana, architecting modern web experiences and building scalable solutions for the African tech ecosystem.",
      location: "Ghana, West Africa",
      certifiedBadge: "AWS & KCNA Certified",
      internshipsBadge: "3+ Industry Internships",

      // Expertise
      areasOfExpertise: "Areas of Expertise",
      frontendArchitecture: "Frontend Architecture",
      frontendArchitectureDesc: "Designing scalable, maintainable React applications with modern patterns",
      cloudNativeDevelopment: "Cloud-Native Development",
      cloudNativeDevelopmentDesc: "Building and deploying applications using cloud-first approaches",
      fullStackIntegration: "Full-Stack Integration",
      fullStackIntegrationDesc: "Seamlessly connecting frontend with robust backend services",
      developerExperience: "Developer Experience",
      developerExperienceDesc: "Creating tools and workflows that enhance productivity",

      // Skills
      componentArchitecture: "Component Architecture",
      stateManagement: "State Management",
      performanceOptimization: "Performance Optimization",
      designSystems: "Design Systems",
      awsServices: "AWS Services",
      kubernetes: "Kubernetes",
      serverless: "Serverless",
      cicdPipelines: "CI/CD Pipelines",
      apiDesign: "API Design",
      databaseModeling: "Database Modeling",
      authentication: "Authentication",
      realtimeFeatures: "Real-time Features",
      developerTools: "Developer Tools",
      codeQuality: "Code Quality",
      teamLeadership: "Team Leadership",
      mentoring: "Mentoring",

      // Current Status
      currentStatus: "Current Status",
      finalYearProject: "Final Year Project",
      finalYearProjectDesc: "BSc Computer Science - Graduation Aug 2025",
      activelySeekingOpportunities: "Actively Seeking Opportunities",
      activelySeekingOpportunitiesDesc: "Frontend & Full-Stack Developer Roles",
      cloudCertifiedProfessional: "Cloud Certified Professional",
      cloudCertifiedProfessionalDesc: "AWS Cloud Practitioner & KCNA",

      // Career Timeline
      careerTimeline: "Career Timeline",
      kcnaCertified: "KCNA Certified",
      kcnaCertifiedDesc: "Kubernetes & Cloud Native Associate",
      awsCloudPractitionerCertified: "AWS Cloud Practitioner Certified",
      awsCloudPractitionerCertifiedDesc: "Amazon Web Services",
      finalYearProjectTimeline: "Graduation Expected August 2025",
      presidentCSS: "President, CS Society @ UDS",
      presidentCSSDesc: "University for Development Studies",
      softwareDevelopmentIntern: "Software Development Intern",
      amalitech: "AmaliTech",
      techIntern: "Tech Intern",
      wikimediaFoundation: "Wikimedia Foundation",
      intern: "Intern",
      agriculturalDevelopmentBank: "Agricultural Development Bank",
      bscComputerScience: "BSc Computer Science",
      universityForDevelopmentStudies: "University for Development Studies",

      // Timeline types
      certification: "certification",
      leadership: "leadership",
      experience: "experience",
      education: "education",

      // Projects
      featuredProjects: "Featured Projects",
      featuredProjectsDesc: "Real-world applications that solve practical problems and create value for users across different industries.",
      viewMoreOnGithub: "View More on GitHub",
      liveDemo: "Live Demo",
      viewContract: "View Smart Contract",
      smartContract: "Smart Contract",
      privateRepository: "Private Repository - Client/Startup Project",
      careerTimeline: "Career Timeline",
      careerTimelineDesc: "My educational journey, certifications, and professional experiences that shaped my expertise in software development and cloud technologies.",

      // Project descriptions
      payvoicerDesc: "Voice-based invoice generation platform that revolutionizes how businesses create and manage invoices using AI-powered voice recognition.",
      payvoicerImpact: "30+ businesses automated",
      cemNewsroomDesc: "Full-featured CMS-powered news website with real-time publishing, content management, and responsive design for modern journalism.",
      cemNewsroomImpact: "1000+ daily readers",
      lastStopEnterpriseDesc: "Comprehensive logistics and goods tracking application with real-time updates, route optimization, and customer notifications.",
      lastStopEnterpriseImpact: "50+ deliveries tracked",
      geomancyCoachingDesc: "Complete ecosystem platform for spiritual and commercial geomancy services with booking system and resource library.",
      geomancyCoachingImpact: "200+ consultations booked",
      hostelBookingDesc: "Real-time hostel booking platform with integrated Stripe payments, room management, and student dashboard.",
      hostelBookingImpact: "300+ bookings processed",
      landformDappDesc: "Modern Web3 Land Ownership DApp built with React + Vite, featuring NFT-based land ownership, IPFS storage, and comprehensive blockchain integration. Includes wallet connectivity via RainbowKit, smart contract interactions, and decentralized land verification system.",
      landformDappImpact: "Web3 Land Ownership Platform",

      payvoicerDesc: "Comprehensive GRA-compliant invoicing SaaS for Ghanaian businesses. Features GRA E-VAT API integration, Paystack payments, multi-tier subscriptions, and secure client management with public invoice sharing capabilities.",
      payvoicerImpact: "GRA-Compliant Business Solution",

      // Skills Section
      technicalArsenal: "Technical Arsenal",
      technicalExpertise: "Technical Expertise",
      technicalArsenalDesc: "A comprehensive collection of modern technologies, frameworks, and certifications that power innovative, scalable, and user-centric applications.",
      frontendMastery: "Frontend Mastery",
      backendApis: "Backend & APIs",
      cloudDevops: "Cloud & DevOps",
      developmentTools: "Development Tools",
      certificationsLearning: "Certifications & Learning",
      professionalHighlights: "Professional Highlights",
      yearsCoding: "Years Coding",
      projectsBuilt: "Projects Built",
      certifications: "Certifications",
      studentsMentored: "Students Mentored",
      readyToBuild: "Ready to Build Something Amazing Together",

      // Contact Section
      getInTouch: "Get in Touch",
      sendMessage: "Send a Message",
      yourName: "Your Name",
      yourEmail: "Your Email",
      yourMessage: "Your Message",
      sendMessageBtn: "Send Message",
      messageSent: "Message sent!",
      messageThankYou: "Thank you for reaching out. I'll get back to you soon.",
      email: "Email",
      location: "Location",
      ghanaWestAfrica: "Ghana, West Africa",
      status: "Status",
      availableForWork: "Available for Work",
      connectSocial: "Connect on Social",
      availableFor: "Available for",
      fullStackProjects: "Full-stack development projects",
      cloudConsultation: "Cloud architecture consultation",
      technicalMentoring: "Technical mentoring & workshops",
      openSourceCollab: "Open source collaboration",

      // Footer
      footerDescription: "Frontend Specialist & Cloud Practitioner passionate about crafting exceptional web experiences and building scalable solutions for the African tech ecosystem.",
      openToOpportunities: "Open to New Opportunities",
      quickLinks: "Quick Links",
      githubPortfolio: "GitHub Portfolio",
      professionalInfo: "Professional Info",
      bscComputerScienceAug: "BSc Computer Science (Aug 2025)",
      awsKcnaCertified: "AWS & KCNA Certified",
      availableForRoles: "Available for Frontend/Full-Stack Roles",
      footerCopyright: "Crafted with React, TypeScript & Tailwind CSS.",
      footerTagline: "Building the future of African technology, one line of code at a time.",
    }
  },
  es: {
    translation: {
      about: "Acerca de",
      projects: "Proyectos",
      contact: "Contacto",
      letsTalk: "Hablemos",
      greeting: "Hola, soy Abdulai Yorli Iddrisu 👋",
      subtitle: "Especialista Frontend | Certificado AWS & Kubernetes | Construyendo Experiencias Web Modernas",
      availableForHire: "Disponible para Contratación",
      graduatingAugust: "Graduándome en Agosto 2025",
      exploreWork: "🚀 Explora Mi Trabajo",
      getResume: "📄 Obtener Mi CV",
      downloadCV: "Descargar CV",
      projectsBuilt: "Proyectos Construidos",
      internships: "Prácticas Industriales",
      certifications: "Certificaciones",
      studentsMentored: "Estudiantes Mentoreados",
      aboutTitle: "Acerca de Mí",
      availableForRoles: "Disponible para Roles Frontend y Full-Stack",
      aboutDescription: "Un especialista frontend apasionado y profesional de la nube de Ghana, diseñando experiencias web modernas y construyendo soluciones escalables para el ecosistema tecnológico africano.",
      location: "Ghana, África Occidental",
      certifiedBadge: "Certificado AWS & KCNA",
      internshipsBadge: "3+ Prácticas Industriales",
      areasOfExpertise: "Áreas de Especialización",
      frontendArchitecture: "Arquitectura Frontend",
      frontendArchitectureDesc: "Diseñando aplicaciones React escalables y mantenibles con patrones modernos",
      cloudNativeDevelopment: "Desarrollo Nativo en la Nube",
      cloudNativeDevelopmentDesc: "Construyendo y desplegando aplicaciones usando enfoques cloud-first",
      fullStackIntegration: "Integración Full-Stack",
      fullStackIntegrationDesc: "Conectando sin problemas frontend con servicios backend robustos",
      developerExperience: "Experiencia del Desarrollador",
      developerExperienceDesc: "Creando herramientas y flujos de trabajo que mejoran la productividad",
      componentArchitecture: "Arquitectura de Componentes",
      stateManagement: "Gestión de Estado",
      performanceOptimization: "Optimización de Rendimiento",
      designSystems: "Sistemas de Diseño",
      awsServices: "Servicios AWS",
      kubernetes: "Kubernetes",
      serverless: "Sin Servidor",
      cicdPipelines: "Pipelines CI/CD",
      apiDesign: "Diseño de API",
      databaseModeling: "Modelado de Base de Datos",
      authentication: "Autenticación",
      realtimeFeatures: "Características en Tiempo Real",
      developerTools: "Herramientas de Desarrollador",
      codeQuality: "Calidad del Código",
      teamLeadership: "Liderazgo de Equipo",
      mentoring: "Mentoría",
      currentStatus: "Estado Actual",
      finalYearProject: "Proyecto de Último Año",
      finalYearProjectDesc: "Licenciatura en Ciencias de la Computación - Graduación Ago 2025",
      activelySeekingOpportunities: "Buscando Activamente Oportunidades",
      activelySeekingOpportunitiesDesc: "Roles de Desarrollador Frontend y Full-Stack",
      cloudCertifiedProfessional: "Profesional Certificado en la Nube",
      cloudCertifiedProfessionalDesc: "AWS Cloud Practitioner & KCNA",
      careerTimeline: "Línea de Tiempo de Carrera",
      kcnaCertified: "Certificado KCNA",
      kcnaCertifiedDesc: "Kubernetes & Cloud Native Associate",
      awsCloudPractitionerCertified: "Certificado AWS Cloud Practitioner",
      awsCloudPractitionerCertifiedDesc: "Amazon Web Services",
      finalYearProjectTimeline: "Graduación Esperada Agosto 2025",
      presidentCSS: "Presidente, Sociedad CS @ UDS",
      presidentCSSDesc: "Universidad para Estudios de Desarrollo",
      softwareDevelopmentIntern: "Interno de Desarrollo de Software",
      amalitech: "AmaliTech",
      techIntern: "Interno de Tecnología",
      wikimediaFoundation: "Fundación Wikimedia",
      intern: "Interno",
      agriculturalDevelopmentBank: "Banco de Desarrollo Agrícola",
      bscComputerScience: "Licenciatura en Ciencias de la Computación",
      universityForDevelopmentStudies: "Universidad para Estudios de Desarrollo",
      certification: "certificación",
      leadership: "liderazgo",
      experience: "experiencia",
      education: "educación",
      featuredProjects: "Proyectos Destacados",
      featuredProjectsDesc: "Aplicaciones del mundo real que resuelven problemas prácticos y crean valor para usuarios en diferentes industrias.",
      viewMoreOnGithub: "Ver Más en GitHub",
      liveDemo: "Demo en Vivo",
      viewContract: "Ver Contrato Inteligente",
      smartContract: "Contrato Inteligente",
      privateRepository: "Repositorio Privado - Proyecto Cliente/Startup",
      careerTimeline: "Cronología Profesional",
      careerTimelineDesc: "Mi trayectoria educativa, certificaciones y experiencias profesionales que moldearon mi experiencia en desarrollo de software y tecnologías en la nube.",
      payvoicerDesc: "Plataforma de generación de facturas basada en voz que revoluciona cómo las empresas crean y gestionan facturas usando reconocimiento de voz con IA.",
      payvoicerImpact: "30+ empresas automatizadas",
      cemNewsroomDesc: "Sitio web de noticias completo con CMS, publicación en tiempo real, gestión de contenido y diseño responsivo para periodismo moderno.",
      cemNewsroomImpact: "1000+ lectores diarios",
      lastStopEnterpriseDesc: "Aplicación integral de logística y seguimiento de mercancías con actualizaciones en tiempo real, optimización de rutas y notificaciones a clientes.",
      lastStopEnterpriseImpact: "50+ entregas rastreadas",
      geomancyCoachingDesc: "Plataforma ecosistema completa para servicios de geomancia espiritual y comercial con sistema de reservas y biblioteca de recursos.",
      geomancyCoachingImpact: "200+ consultas reservadas",
      hostelBookingDesc: "Plataforma de reservas de hostal en tiempo real con pagos Stripe integrados, gestión de habitaciones y panel de estudiantes.",
      hostelBookingImpact: "300+ reservas procesadas",
      landformDappDesc: "DApp moderna de propiedad de tierras Web3 construida con React + Vite, con propiedad de tierras basada en NFT, almacenamiento IPFS e integración blockchain integral. Incluye conectividad de billetera vía RainbowKit, interacciones de contratos inteligentes y sistema de verificación de tierras descentralizado.",
      landformDappImpact: "Plataforma de Propiedad de Tierras Web3",

      payvoicerDesc: "SaaS integral de facturación compatible con GRA para empresas ghanesas. Incluye integración con API GRA E-VAT, pagos Paystack, suscripciones multinivel y gestión segura de clientes con capacidades de compartir facturas públicas.",
      payvoicerImpact: "Solución Empresarial Compatible con GRA",

      // Skills Section
      technicalArsenal: "Arsenal Técnico",
      technicalExpertise: "Experiencia Técnica",
      technicalArsenalDesc: "Una colección integral de tecnologías modernas, frameworks y certificaciones que impulsan aplicaciones innovadoras, escalables y centradas en el usuario.",
      frontendMastery: "Dominio Frontend",
      backendApis: "Backend y APIs",
      cloudDevops: "Cloud y DevOps",
      developmentTools: "Herramientas de Desarrollo",
      certificationsLearning: "Certificaciones y Aprendizaje",
      professionalHighlights: "Logros Profesionales",
      yearsCoding: "Años Programando",
      projectsBuilt: "Proyectos Construidos",
      certifications: "Certificaciones",
      studentsMentored: "Estudiantes Mentoreados",
      readyToBuild: "Listo para Construir Algo Increíble Juntos",

      // Contact Section
      getInTouch: "Ponte en Contacto",
      sendMessage: "Enviar un Mensaje",
      yourName: "Tu Nombre",
      yourEmail: "Tu Email",
      yourMessage: "Tu Mensaje",
      sendMessageBtn: "Enviar Mensaje",
      messageSent: "¡Mensaje enviado!",
      messageThankYou: "Gracias por contactarme. Te responderé pronto.",
      email: "Email",
      location: "Ubicación",
      ghanaWestAfrica: "Ghana, África Occidental",
      status: "Estado",
      availableForWork: "Disponible para Trabajar",
      connectSocial: "Conectar en Redes Sociales",
      availableFor: "Disponible para",
      fullStackProjects: "Proyectos de desarrollo full-stack",
      cloudConsultation: "Consultoría de arquitectura en la nube",
      technicalMentoring: "Mentoría técnica y talleres",
      openSourceCollab: "Colaboración en código abierto",

      // Footer
      footerDescription: "Especialista Frontend y Practicante de la Nube apasionado por crear experiencias web excepcionales y construir soluciones escalables para el ecosistema tecnológico africano.",
      openToOpportunities: "Abierto a Nuevas Oportunidades",
      quickLinks: "Enlaces Rápidos",
      githubPortfolio: "Portafolio GitHub",
      professionalInfo: "Información Profesional",
      bscComputerScienceAug: "Licenciatura en Ciencias de la Computación (Ago 2025)",
      awsKcnaCertified: "Certificado AWS y KCNA",
      availableForRoles: "Disponible para Roles Frontend/Full-Stack",
      footerCopyright: "Creado con React, TypeScript y Tailwind CSS.",
      footerTagline: "Construyendo el futuro de la tecnología africana, una línea de código a la vez.",
    }
  },
  fr: {
    translation: {
      about: "À propos",
      projects: "Projets",
      contact: "Contact",
      letsTalk: "Parlons",
      greeting: "Salut, je suis Abdulai Yorli Iddrisu 👋",
      subtitle: "Spécialiste Frontend | Certifié AWS & Kubernetes | Créant des Expériences Web Modernes",
      availableForHire: "Disponible pour Embauche",
      graduatingAugust: "Diplômé en Août 2025",
      exploreWork: "🚀 Explorer Mon Travail",
      getResume: "📄 Obtenir Mon CV",
      downloadCV: "Télécharger CV",
      projectsBuilt: "Projets Construits",
      internships: "Stages Industriels",
      certifications: "Certifications",
      studentsMentored: "Étudiants Mentorés",
      aboutTitle: "À Propos de Moi",
      availableForRoles: "Disponible pour des Rôles Frontend et Full-Stack",
      aboutDescription: "Un spécialiste frontend passionné et praticien du cloud du Ghana, architecturant des expériences web modernes et construisant des solutions évolutives pour l'écosystème technologique africain.",
      location: "Ghana, Afrique de l'Ouest",
      certifiedBadge: "Certifié AWS & KCNA",
      internshipsBadge: "3+ Stages Industriels",
      areasOfExpertise: "Domaines d'Expertise",
      frontendArchitecture: "Architecture Frontend",
      frontendArchitectureDesc: "Conception d'applications React évolutives et maintenables avec des patterns modernes",
      cloudNativeDevelopment: "Développement Cloud-Native",
      cloudNativeDevelopmentDesc: "Construction et déploiement d'applications utilisant des approches cloud-first",
      fullStackIntegration: "Intégration Full-Stack",
      fullStackIntegrationDesc: "Connexion transparente du frontend avec des services backend robustes",
      developerExperience: "Expérience Développeur",
      developerExperienceDesc: "Création d'outils et de workflows qui améliorent la productivité",
      componentArchitecture: "Architecture de Composants",
      stateManagement: "Gestion d'État",
      performanceOptimization: "Optimisation des Performances",
      designSystems: "Systèmes de Design",
      awsServices: "Services AWS",
      kubernetes: "Kubernetes",
      serverless: "Sans Serveur",
      cicdPipelines: "Pipelines CI/CD",
      apiDesign: "Conception d'API",
      databaseModeling: "Modélisation de Base de Données",
      authentication: "Authentification",
      realtimeFeatures: "Fonctionnalités Temps Réel",
      developerTools: "Outils Développeur",
      codeQuality: "Qualité du Code",
      teamLeadership: "Leadership d'Équipe",
      mentoring: "Mentorat",
      currentStatus: "Statut Actuel",
      finalYearProject: "Projet de Dernière Année",
      finalYearProjectDesc: "Licence en Informatique - Diplôme Août 2025",
      activelySeekingOpportunities: "Recherche Active d'Opportunités",
      activelySeekingOpportunitiesDesc: "Rôles Développeur Frontend et Full-Stack",
      cloudCertifiedProfessional: "Professionnel Certifié Cloud",
      cloudCertifiedProfessionalDesc: "AWS Cloud Practitioner & KCNA",
      careerTimeline: "Chronologie de Carrière",
      kcnaCertified: "Certifié KCNA",
      kcnaCertifiedDesc: "Kubernetes & Cloud Native Associate",
      awsCloudPractitionerCertified: "Certifié AWS Cloud Practitioner",
      awsCloudPractitionerCertifiedDesc: "Amazon Web Services",
      finalYearProjectTimeline: "Diplôme Attendu Août 2025",
      presidentCSS: "Président, Société CS @ UDS",
      presidentCSSDesc: "Université pour les Études de Développement",
      softwareDevelopmentIntern: "Stagiaire Développement Logiciel",
      amalitech: "AmaliTech",
      techIntern: "Stagiaire Tech",
      wikimediaFoundation: "Fondation Wikimedia",
      intern: "Stagiaire",
      agriculturalDevelopmentBank: "Banque de Développement Agricole",
      bscComputerScience: "Licence en Informatique",
      universityForDevelopmentStudies: "Université pour les Études de Développement",
      certification: "certification",
      leadership: "leadership",
      experience: "expérience",
      education: "éducation",
      featuredProjects: "Projets Vedettes",
      featuredProjectsDesc: "Applications du monde réel qui résolvent des problèmes pratiques et créent de la valeur pour les utilisateurs dans différentes industries.",
      viewMoreOnGithub: "Voir Plus sur GitHub",
      liveDemo: "Démo en Direct",
      viewContract: "Voir Contrat Intelligent",
      smartContract: "Contrat Intelligent",
      privateRepository: "Dépôt Privé - Projet Client/Startup",
      careerTimeline: "Chronologie de Carrière",
      careerTimelineDesc: "Mon parcours éducatif, certifications et expériences professionnelles qui ont façonné mon expertise en développement logiciel et technologies cloud.",
      payvoicerDesc: "Plateforme de génération de factures basée sur la voix qui révolutionne la façon dont les entreprises créent et gèrent les factures en utilisant la reconnaissance vocale alimentée par l'IA.",
      payvoicerImpact: "30+ entreprises automatisées",
      cemNewsroomDesc: "Site web d'actualités complet alimenté par CMS avec publication en temps réel, gestion de contenu et design responsive pour le journalisme moderne.",
      cemNewsroomImpact: "1000+ lecteurs quotidiens",
      lastStopEnterpriseDesc: "Application complète de logistique et de suivi des marchandises avec mises à jour en temps réel, optimisation des itinéraires et notifications clients.",
      lastStopEnterpriseImpact: "50+ livraisons suivies",
      geomancyCoachingDesc: "Plateforme écosystème complète pour les services de géomancie spirituelle et commerciale avec système de réservation et bibliothèque de ressources.",
      geomancyCoachingImpact: "200+ consultations réservées",
      hostelBookingDesc: "Plateforme de réservation d'auberge en temps réel avec paiements Stripe intégrés, gestion des chambres et tableau de bord étudiant.",
      hostelBookingImpact: "300+ réservations traitées",
      landformDappDesc: "DApp moderne de propriété foncière Web3 construite avec React + Vite, avec propriété foncière basée sur NFT, stockage IPFS et intégration blockchain complète. Inclut la connectivité de portefeuille via RainbowKit, interactions de contrats intelligents et système de vérification foncière décentralisé.",
      landformDappImpact: "Plateforme de Propriété Foncière Web3",

      payvoicerDesc: "SaaS de facturation conforme GRA complet pour les entreprises ghanéennes. Comprend l'intégration API GRA E-VAT, paiements Paystack, abonnements multi-niveaux et gestion sécurisée des clients avec capacités de partage de factures publiques.",
      payvoicerImpact: "Solution d'Entreprise Conforme GRA",

      // Skills Section
      technicalArsenal: "Arsenal Technique",
      technicalExpertise: "Expertise Technique",
      technicalArsenalDesc: "Une collection complète de technologies modernes, frameworks et certifications qui alimentent des applications innovantes, évolutives et centrées sur l'utilisateur.",
      frontendMastery: "Maîtrise Frontend",
      backendApis: "Backend et APIs",
      cloudDevops: "Cloud et DevOps",
      developmentTools: "Outils de Développement",
      certificationsLearning: "Certifications et Apprentissage",
      professionalHighlights: "Points Forts Professionnels",
      yearsCoding: "Années de Programmation",
      projectsBuilt: "Projets Construits",
      certifications: "Certifications",
      studentsMentored: "Étudiants Mentorés",
      readyToBuild: "Prêt à Construire Quelque Chose d'Incroyable Ensemble",

      // Contact Section
      getInTouch: "Entrer en Contact",
      sendMessage: "Envoyer un Message",
      yourName: "Votre Nom",
      yourEmail: "Votre Email",
      yourMessage: "Votre Message",
      sendMessageBtn: "Envoyer le Message",
      messageSent: "Message envoyé !",
      messageThankYou: "Merci de m'avoir contacté. Je vous répondrai bientôt.",
      email: "Email",
      location: "Localisation",
      ghanaWestAfrica: "Ghana, Afrique de l'Ouest",
      status: "Statut",
      availableForWork: "Disponible pour Travailler",
      connectSocial: "Se Connecter sur les Réseaux Sociaux",
      availableFor: "Disponible pour",
      fullStackProjects: "Projets de développement full-stack",
      cloudConsultation: "Consultation d'architecture cloud",
      technicalMentoring: "Mentorat technique et ateliers",
      openSourceCollab: "Collaboration open source",

      // Footer
      footerDescription: "Spécialiste Frontend et Praticien Cloud passionné par la création d'expériences web exceptionnelles et la construction de solutions évolutives pour l'écosystème technologique africain.",
      openToOpportunities: "Ouvert aux Nouvelles Opportunités",
      quickLinks: "Liens Rapides",
      githubPortfolio: "Portfolio GitHub",
      professionalInfo: "Informations Professionnelles",
      bscComputerScienceAug: "Licence en Informatique (Août 2025)",
      awsKcnaCertified: "Certifié AWS et KCNA",
      availableForRoles: "Disponible pour des Rôles Frontend/Full-Stack",
      footerCopyright: "Créé avec React, TypeScript et Tailwind CSS.",
      footerTagline: "Construire l'avenir de la technologie africaine, une ligne de code à la fois.",
    }
  },
  de: {
    translation: {
      about: "Über mich",
      projects: "Projekte",
      contact: "Kontakt",
      letsTalk: "Lass uns reden",
      greeting: "Hi, ich bin Abdulai Yorli Iddrisu 👋",
      subtitle: "Frontend-Spezialist | AWS & Kubernetes Zertifiziert | Moderne Web-Erfahrungen schaffen",
      availableForHire: "Verfügbar für Anstellung",
      graduatingAugust: "Abschluss August 2025",
      exploreWork: "🚀 Meine Arbeit erkunden",
      getResume: "📄 Meinen Lebenslauf holen",
      downloadCV: "CV Herunterladen",
      projectsBuilt: "Projekte Erstellt",
      internships: "Industrie-Praktika",
      certifications: "Zertifizierungen",
      studentsMentored: "Betreute Studenten",
      aboutTitle: "Über Mich",
      availableForRoles: "Verfügbar für Frontend & Full-Stack Rollen",
      aboutDescription: "Ein leidenschaftlicher Frontend-Spezialist und Cloud-Praktiker aus Ghana, der moderne Web-Erfahrungen architektiert und skalierbare Lösungen für das afrikanische Tech-Ökosystem entwickelt.",
      location: "Ghana, Westafrika",
      certifiedBadge: "AWS & KCNA Zertifiziert",
      internshipsBadge: "3+ Industrie-Praktika",
      areasOfExpertise: "Fachbereiche",
      frontendArchitecture: "Frontend-Architektur",
      frontendArchitectureDesc: "Entwicklung skalierbarer, wartbarer React-Anwendungen mit modernen Mustern",
      cloudNativeDevelopment: "Cloud-Native Entwicklung",
      cloudNativeDevelopmentDesc: "Erstellung und Bereitstellung von Anwendungen mit Cloud-First-Ansätzen",
      fullStackIntegration: "Full-Stack Integration",
      fullStackIntegrationDesc: "Nahtlose Verbindung von Frontend mit robusten Backend-Services",
      developerExperience: "Entwicklererfahrung",
      developerExperienceDesc: "Erstellung von Tools und Workflows, die die Produktivität steigern",
      componentArchitecture: "Komponenten-Architektur",
      stateManagement: "State Management",
      performanceOptimization: "Performance-Optimierung",
      designSystems: "Design-Systeme",
      awsServices: "AWS Services",
      kubernetes: "Kubernetes",
      serverless: "Serverless",
      cicdPipelines: "CI/CD Pipelines",
      apiDesign: "API Design",
      databaseModeling: "Datenbank-Modellierung",
      authentication: "Authentifizierung",
      realtimeFeatures: "Echtzeit-Features",
      developerTools: "Entwickler-Tools",
      codeQuality: "Code-Qualität",
      teamLeadership: "Team-Leadership",
      mentoring: "Mentoring",
      currentStatus: "Aktueller Status",
      finalYearProject: "Abschlussprojekt",
      finalYearProjectDesc: "BSc Informatik - Abschluss Aug 2025",
      activelySeekingOpportunities: "Aktiv auf Jobsuche",
      activelySeekingOpportunitiesDesc: "Frontend & Full-Stack Entwickler Rollen",
      cloudCertifiedProfessional: "Cloud Zertifizierter Profi",
      cloudCertifiedProfessionalDesc: "AWS Cloud Practitioner & KCNA",
      careerTimeline: "Karriere-Zeitleiste",
      kcnaCertified: "KCNA Zertifiziert",
      kcnaCertifiedDesc: "Kubernetes & Cloud Native Associate",
      awsCloudPractitionerCertified: "AWS Cloud Practitioner Zertifiziert",
      awsCloudPractitionerCertifiedDesc: "Amazon Web Services",
      finalYearProjectTimeline: "Abschluss erwartet August 2025",
      presidentCSS: "Präsident, CS Gesellschaft @ UDS",
      presidentCSSDesc: "Universität für Entwicklungsstudien",
      softwareDevelopmentIntern: "Software-Entwicklungs-Praktikant",
      amalitech: "AmaliTech",
      techIntern: "Tech-Praktikant",
      wikimediaFoundation: "Wikimedia Foundation",
      intern: "Praktikant",
      agriculturalDevelopmentBank: "Landwirtschaftliche Entwicklungsbank",
      bscComputerScience: "BSc Informatik",
      universityForDevelopmentStudies: "Universität für Entwicklungsstudien",
      certification: "Zertifizierung",
      leadership: "Führung",
      experience: "Erfahrung",
      education: "Bildung",
      featuredProjects: "Ausgewählte Projekte",
      featuredProjectsDesc: "Reale Anwendungen, die praktische Probleme lösen und Wert für Nutzer in verschiedenen Branchen schaffen.",
      viewMoreOnGithub: "Mehr auf GitHub ansehen",
      liveDemo: "Live Demo",
      viewContract: "Smart Contract Ansehen",
      smartContract: "Smart Contract",
      privateRepository: "Privates Repository - Kunden-/Startup-Projekt",
      careerTimeline: "Karriere-Zeitlinie",
      careerTimelineDesc: "Mein Bildungsweg, Zertifizierungen und berufliche Erfahrungen, die meine Expertise in Softwareentwicklung und Cloud-Technologien geprägt haben.",
      payvoicerDesc: "Sprachbasierte Rechnungserstellungsplattform, die revolutioniert, wie Unternehmen Rechnungen mit KI-gestützter Spracherkennung erstellen und verwalten.",
      payvoicerImpact: "30+ Unternehmen automatisiert",
      cemNewsroomDesc: "Vollausgestattete CMS-gestützte News-Website mit Echtzeit-Publishing, Content-Management und responsivem Design für modernen Journalismus.",
      cemNewsroomImpact: "1000+ tägliche Leser",
      lastStopEnterpriseDesc: "Umfassende Logistik- und Warenverfolgungs-Anwendung mit Echtzeit-Updates, Routenoptimierung und Kundenbenachrichtigungen.",
      lastStopEnterpriseImpact: "50+ Lieferungen verfolgt",
      geomancyCoachingDesc: "Vollständige Ökosystem-Plattform für spirituelle und kommerzielle Geomantie-Services mit Buchungssystem und Ressourcenbibliothek.",
      geomancyCoachingImpact: "200+ Beratungen gebucht",
      hostelBookingDesc: "Echtzeit-Hostel-Buchungsplattform mit integrierten Stripe-Zahlungen, Zimmerverwaltung und Studenten-Dashboard.",
      hostelBookingImpact: "300+ Buchungen verarbeitet",
      landformDappDesc: "Moderne Web3-Landbesitz-DApp gebaut mit React + Vite, mit NFT-basiertem Landbesitz, IPFS-Speicher und umfassender Blockchain-Integration. Beinhaltet Wallet-Konnektivität über RainbowKit, Smart-Contract-Interaktionen und dezentrales Landverifizierungssystem.",
      landformDappImpact: "Web3-Landbesitz-Plattform",

      payvoicerDesc: "Umfassende GRA-konforme Rechnungs-SaaS für ghanaische Unternehmen. Beinhaltet GRA E-VAT API-Integration, Paystack-Zahlungen, mehrstufige Abonnements und sichere Kundenverwaltung mit öffentlichen Rechnungsfreigabe-Funktionen.",
      payvoicerImpact: "GRA-konforme Unternehmenslösung",

      // Skills Section
      technicalArsenal: "Technisches Arsenal",
      technicalExpertise: "Technische Expertise",
      technicalArsenalDesc: "Eine umfassende Sammlung moderner Technologien, Frameworks und Zertifizierungen, die innovative, skalierbare und benutzerzentrierte Anwendungen antreiben.",
      frontendMastery: "Frontend-Meisterschaft",
      backendApis: "Backend & APIs",
      cloudDevops: "Cloud & DevOps",
      developmentTools: "Entwicklungstools",
      certificationsLearning: "Zertifizierungen & Lernen",
      professionalHighlights: "Berufliche Höhepunkte",
      yearsCoding: "Jahre Programmierung",
      projectsBuilt: "Projekte Erstellt",
      certifications: "Zertifizierungen",
      studentsMentored: "Betreute Studenten",
      readyToBuild: "Bereit, Gemeinsam Etwas Erstaunliches zu Schaffen",

      // Contact Section
      getInTouch: "Kontakt Aufnehmen",
      sendMessage: "Nachricht Senden",
      yourName: "Ihr Name",
      yourEmail: "Ihre E-Mail",
      yourMessage: "Ihre Nachricht",
      sendMessageBtn: "Nachricht Senden",
      messageSent: "Nachricht gesendet!",
      messageThankYou: "Danke, dass Sie sich gemeldet haben. Ich werde mich bald bei Ihnen melden.",
      email: "E-Mail",
      location: "Standort",
      ghanaWestAfrica: "Ghana, Westafrika",
      status: "Status",
      availableForWork: "Verfügbar für Arbeit",
      connectSocial: "Auf Social Media Verbinden",
      availableFor: "Verfügbar für",
      fullStackProjects: "Full-Stack-Entwicklungsprojekte",
      cloudConsultation: "Cloud-Architektur-Beratung",
      technicalMentoring: "Technisches Mentoring & Workshops",
      openSourceCollab: "Open-Source-Zusammenarbeit",

      // Footer
      footerDescription: "Frontend-Spezialist und Cloud-Praktiker mit Leidenschaft für außergewöhnliche Web-Erfahrungen und skalierbare Lösungen für das afrikanische Tech-Ökosystem.",
      openToOpportunities: "Offen für Neue Möglichkeiten",
      quickLinks: "Schnelle Links",
      githubPortfolio: "GitHub Portfolio",
      professionalInfo: "Berufliche Informationen",
      bscComputerScienceAug: "BSc Informatik (Aug 2025)",
      awsKcnaCertified: "AWS & KCNA Zertifiziert",
      availableForRoles: "Verfügbar für Frontend/Full-Stack Rollen",
      footerCopyright: "Erstellt mit React, TypeScript & Tailwind CSS.",
      footerTagline: "Die Zukunft der afrikanischen Technologie aufbauen, eine Codezeile nach der anderen.",
    }
  }
}

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  })

export default i18n
