<svg width="500" height="300" viewBox="0 0 500 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="500" height="300" fill="url(#geomancyGradient)"/>
  
  <!-- Geometric patterns representing geomancy -->
  <g opacity="0.1">
    <!-- Sacred geometry patterns -->
    <circle cx="100" cy="75" r="30" stroke="#4F46E5" stroke-width="2" fill="none"/>
    <circle cx="400" cy="225" r="25" stroke="#7C3AED" stroke-width="2" fill="none"/>
    <polygon points="250,50 280,100 220,100" stroke="#059669" stroke-width="2" fill="none"/>
    <polygon points="350,150 380,200 320,200" stroke="#DC2626" stroke-width="2" fill="none"/>
    
    <!-- Connecting lines -->
    <line x1="100" y1="75" x2="250" y2="75" stroke="#6B7280" stroke-width="1" opacity="0.5"/>
    <line x1="250" y1="100" x2="350" y2="150" stroke="#6B7280" stroke-width="1" opacity="0.5"/>
    <line x1="350" y1="200" x2="400" y2="225" stroke="#6B7280" stroke-width="1" opacity="0.5"/>
  </g>
  
  <!-- Main content area -->
  <rect x="50" y="80" width="400" height="140" rx="12" fill="rgba(255,255,255,0.9)" stroke="rgba(79,70,229,0.2)" stroke-width="1"/>
  
  <!-- Header -->
  <rect x="70" y="100" width="360" height="40" rx="8" fill="rgba(79,70,229,0.1)"/>
  <text x="250" y="125" text-anchor="middle" fill="#4F46E5" font-family="Arial, sans-serif" font-size="18" font-weight="600">
    GeomancyCoaching.org
  </text>
  
  <!-- Content sections -->
  <rect x="70" y="150" width="100" height="60" rx="6" fill="rgba(16,185,129,0.1)" stroke="rgba(16,185,129,0.3)" stroke-width="1"/>
  <text x="120" y="170" text-anchor="middle" fill="#059669" font-family="Arial, sans-serif" font-size="10" font-weight="500">
    Coaching
  </text>
  <text x="120" y="185" text-anchor="middle" fill="#059669" font-family="Arial, sans-serif" font-size="10">
    Sessions
  </text>
  
  <rect x="180" y="150" width="100" height="60" rx="6" fill="rgba(124,58,237,0.1)" stroke="rgba(124,58,237,0.3)" stroke-width="1"/>
  <text x="230" y="170" text-anchor="middle" fill="#7C3AED" font-family="Arial, sans-serif" font-size="10" font-weight="500">
    Resources
  </text>
  <text x="230" y="185" text-anchor="middle" fill="#7C3AED" font-family="Arial, sans-serif" font-size="10">
    Library
  </text>
  
  <rect x="290" y="150" width="100" height="60" rx="6" fill="rgba(220,38,38,0.1)" stroke="rgba(220,38,38,0.3)" stroke-width="1"/>
  <text x="340" y="170" text-anchor="middle" fill="#DC2626" font-family="Arial, sans-serif" font-size="10" font-weight="500">
    Booking
  </text>
  <text x="340" y="185" text-anchor="middle" fill="#DC2626" font-family="Arial, sans-serif" font-size="10">
    System
  </text>
  
  <!-- Tech stack indicator -->
  <text x="250" y="245" text-anchor="middle" fill="#6B7280" font-family="Arial, sans-serif" font-size="12" font-weight="500">
    React • TypeScript • Vite • Tailwind CSS
  </text>
  
  <!-- Status indicator -->
  <circle cx="420" cy="110" r="8" fill="#F59E0B"/>
  <text x="440" y="115" fill="#92400E" font-family="Arial, sans-serif" font-size="10" font-weight="500">
    In Development
  </text>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="geomancyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F8FAFC;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F1F5F9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E2E8F0;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
